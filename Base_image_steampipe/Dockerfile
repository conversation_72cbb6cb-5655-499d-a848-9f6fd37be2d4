# Base image for all Steampipe compliance services
FROM ubuntu:22.04 AS base

# Prevent interactive prompts during package installation
ENV DEBIAN_FRONTEND=noninteractive

# Define required versions
ENV STEAMPIPE_VERSION=1.0.3
ENV POWERPIPE_VERSION=1.2.8

# Force apt to accept repositories with invalid GPG signatures
RUN echo 'Acquire::AllowInsecureRepositories "true";' > /etc/apt/apt.conf.d/90insecure && \
    echo 'Acquire::AllowDowngradeToInsecureRepositories "true";' >> /etc/apt/apt.conf.d/90insecure && \
    echo 'APT::Get::AllowUnauthenticated "true";' >> /etc/apt/apt.conf.d/90insecure

# Update and install GPG key utilities first
RUN apt-get update && apt-get install -y --no-install-recommends --allow-unauthenticated \
    gnupg ca-certificates && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/* /var/cache/apt/archives/*

# Install build-essential separately
RUN apt-get update && apt-get install -y build-essential && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Try to fix GPG keys for Ubuntu repositories
RUN apt-key adv --keyserver keyserver.ubuntu.com --recv-keys 871920D1991BC93C || true && \
    apt-key adv --keyserver keyserver.ubuntu.com --recv-keys 3B4FE6ACC0B21F32 || true

# Install essential dependencies including Go build tools
RUN apt-get update && apt-get install -y --no-install-recommends --allow-unauthenticated \
    wget software-properties-common git curl sqlite3 apt-transport-https lsb-release make gcc g++ unzip tar \
    python3 python3-pip python3-dev && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/* /var/cache/apt/archives/*

# Install Go 1.23.1 manually - required for plugin compatibility
RUN ARCH=$(dpkg --print-architecture) && \
    if [ "$ARCH" = "amd64" ]; then \
        GO_ARCH="amd64"; \
    elif [ "$ARCH" = "arm64" ]; then \
        GO_ARCH="arm64"; \
    else \
        echo "Unsupported architecture: $ARCH" && exit 1; \
    fi && \
    wget https://go.dev/dl/go1.23.1.linux-${GO_ARCH}.tar.gz && \
    tar -C /usr/local -xzf go1.23.1.linux-${GO_ARCH}.tar.gz && \
    rm go1.23.1.linux-${GO_ARCH}.tar.gz
ENV PATH=$PATH:/usr/local/go/bin

# Install Google Cloud SDK
RUN mkdir -p /etc/apt/keyrings && \
    curl -fsSL https://packages.cloud.google.com/apt/doc/apt-key.gpg | gpg --dearmor -o /etc/apt/keyrings/google-cloud-keyring.gpg && \
    echo "deb [signed-by=/etc/apt/keyrings/google-cloud-keyring.gpg] https://packages.cloud.google.com/apt cloud-sdk main" | tee /etc/apt/sources.list.d/google-cloud-sdk.list && \
    apt-get update && apt-get install -y --no-install-recommends --allow-unauthenticated google-cloud-cli && \
    apt-get clean && rm -rf /var/lib/apt/lists/* /var/cache/apt/archives/*

# Create a non-root user
RUN useradd -m -s /bin/bash steampipe

# Set up required directories
RUN mkdir -p /home/<USER>/.steampipe/db/14.2.0 \
    /home/<USER>/.steampipe/plugins \
    /home/<USER>/.steampipe/logs \
    /home/<USER>/.steampipe/internal \
    /home/<USER>/.steampipe/config \
    /home/<USER>/.powerpipe \
    /home/<USER>/.config/gcloud \
    /home/<USER>/go/src \
    /build \
    /app && \
    chown -R steampipe:steampipe /home/<USER>/app /build

# Install Steampipe
RUN curl -sSL https://steampipe.io/install/steampipe.sh | sh && \
    chown steampipe:steampipe /usr/local/bin/steampipe

# Install Powerpipe separately
RUN cd /tmp && \
    ARCH=$(dpkg --print-architecture) && \
    if [ "$ARCH" = "amd64" ]; then \
        POWERPIPE_ARCH="amd64"; \
    elif [ "$ARCH" = "arm64" ]; then \
        POWERPIPE_ARCH="arm64"; \
    else \
        echo "Unsupported architecture: $ARCH" && exit 1; \
    fi && \
    wget https://github.com/turbot/powerpipe/releases/download/v${POWERPIPE_VERSION}/powerpipe.linux.${POWERPIPE_ARCH}.tar.gz && \
    tar -xzf powerpipe.linux.${POWERPIPE_ARCH}.tar.gz && \
    mv powerpipe /usr/local/bin/ && \
    chmod +x /usr/local/bin/powerpipe && \
    chown steampipe:steampipe /usr/local/bin/powerpipe && \
    rm powerpipe.linux.${POWERPIPE_ARCH}.tar.gz

# Switch to non-root user
USER steampipe

# Configure Go environment
ENV GOPATH=/home/<USER>/go
ENV PATH=$PATH:/home/<USER>/go/bin:/usr/local/go/bin
ENV HOME=/home/<USER>

# Don't install plugins here - they will be installed at runtime in service containers
# This avoids permission issues when switching users in child containers

# Verify basic installs
RUN steampipe --version && powerpipe --version

WORKDIR /app