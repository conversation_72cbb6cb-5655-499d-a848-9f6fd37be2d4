{"group_id": "root_result_group", "title": "All Controls", "description": "", "tags": {}, "summary": {"status": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 202}}, "groups": [{"group_id": "gcp_compliance.benchmark.all_controls", "title": "All Controls", "description": "This benchmark contains all controls grouped by service to help you detect resource configurations that do not meet best practices.", "tags": {"category": "Compliance", "plugin": "gcp", "service": "GCP", "type": "Benchmark"}, "summary": {"status": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 202}}, "groups": [{"group_id": "gcp_compliance.benchmark.all_controls_alloydb", "title": "AlloyDB", "description": "This section contains recommendations for configuring Alloy DB resources.", "tags": {"category": "Compliance", "plugin": "gcp", "service": "GCP/AlloyDB", "type": "Benchmark"}, "summary": {"status": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 4}}, "groups": [], "controls": [{"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.alloydb_cluster_encrypted_with_cmk", "description": "", "severity": "", "tags": {"category": "Compliance", "plugin": "gcp", "service": "GCP/AlloyDB", "soc_2_2017": "true"}, "title": "Alloy DB clusters should use customer-managed encryption key (CMEK) for encryption", "run_status": 8, "run_error": "relation \"gcp_alloydb_cluster\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.alloydb_instance_log_error_verbosity_database_flag_default_or_stricter", "description": "The log_error_verbosity flag controls the verbosity/details of messages logged. Valid values are: 'TERSE', 'DEFAULT', and 'VERBOSE'.", "severity": "", "tags": {"category": "Compliance", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "plugin": "gcp", "service": "GCP/AlloyDB", "soc_2_2017": "true"}, "title": "Ensure 'log_error_verbosity' database flag for Alloy DB instance is set to 'DEFAULT' or stricter", "run_status": 8, "run_error": "relation \"gcp_alloydb_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.alloydb_instance_log_min_error_statement_database_flag_configured", "description": "The log_min_error_statement flag defines the minimum message severity level that are considered as an error statement. Messages for error statements are logged with the SQL statement. Valid values include DEBUG5, DEBUG4, DEBUG3, DEBUG2, DEBUG1, INFO, NOTICE, WARNING, ERROR, LOG, FATAL, and PANIC. Each severity level includes the subsequent levels mentioned above. Ensure a value of ERROR or stricter is set.", "severity": "", "tags": {"category": "Compliance", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "plugin": "gcp", "service": "GCP/AlloyDB", "soc_2_2017": "true"}, "title": "Ensure 'log_min_error_statement' database flag for Alloy DB instance is set to 'Error' or stricter", "run_status": 8, "run_error": "relation \"gcp_alloydb_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.alloydb_instance_log_min_messages_database_flag_error", "description": "The log_min_messages flag defines the minimum message severity level that is considered as an error statement. Messages for error statements are logged with the SQL statement. Valid values include DEBUG5, DEBUG4, DEBUG3, DEBUG2, DEBUG1, INFO, NOTICE, WARNING, ERROR, LOG, FATAL, and PANIC. Each severity level includes the subsequent levels mentioned above.", "severity": "", "tags": {"category": "Compliance", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "plugin": "gcp", "service": "GCP/AlloyDB", "soc_2_2017": "true"}, "title": "Ensure that the 'Log_min_messages' Flag for a Alloy DB Instance is set at minimum to 'Warning'", "run_status": 8, "run_error": "relation \"gcp_alloydb_instance\" does not exist (SQLSTATE 42P01)"}]}, {"group_id": "gcp_compliance.benchmark.all_controls_appengine", "title": "App Engine", "description": "This section contains recommendations for configuring App Engine resources.", "tags": {"category": "Compliance", "plugin": "gcp", "service": "GCP/AppEngine", "type": "Benchmark"}, "summary": {"status": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}}, "groups": [], "controls": [{"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.app_engine_application_iap_enabled", "description": "This control ensures that App Engine application IAP(Identity-Aware Proxy) is enabled. IAP is used to enforce access control policies for applications and resources. Activating Identity-Aware Proxy (IAP) is a suggested practice for enhancing the security of your App Engine application.", "severity": "", "tags": {"category": "Compliance", "plugin": "gcp", "service": "GCP/AppEngine"}, "title": "App Engine application IAP should be enabled", "run_status": 8, "run_error": "relation \"gcp_app_engine_application\" does not exist (SQLSTATE 42P01)"}]}, {"group_id": "gcp_compliance.benchmark.all_controls_bigquery", "title": "<PERSON><PERSON><PERSON><PERSON>", "description": "This section contains recommendations for configuring BigQuery resources.", "tags": {"category": "Compliance", "plugin": "gcp", "service": "GCP/BigQuery", "type": "Benchmark"}, "summary": {"status": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 5}}, "groups": [], "controls": [{"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.bigquery_dataset_encrypted_with_cmk", "description": "BigQuery by default encrypts the data as rest by employing Envelope Encryption using Google managed cryptographic keys. The data is encrypted using the data encryption keys and data encryption keys themselves are further encrypted using key encryption keys. This is seamless and does not require any additional input from the user. However, if you want to have greater control, Customer-managed encryption keys (CMEK) can be used as encryption key management solutions for BigQuery Data Sets.", "severity": "", "tags": {"category": "Compliance", "hipaa": "true", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "plugin": "gcp", "service": "GCP/BigQuery", "soc_2_2017": "true"}, "title": "Ensure that a default customer-managed encryption key (CMEK) is specified for all BigQuery Data Sets", "run_status": 8, "run_error": "relation \"gcp_bigquery_dataset\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.bigquery_table_encrypted_with_cmk", "description": "BigQuery by default encrypts the data as rest by employing Envelope Encryption using Google managed cryptographic keys. The data is encrypted using the data encryption keys and data encryption keys themselves are further encrypted using key encryption keys. This is seamless and does not require any additional input from the user. However, if you want to have greater control, Customer-managed encryption keys (CMEK) can be used as encryption key management solutions for BigQuery Data Sets. If CMEK is used, the CMEK is used to encrypt the data encryption keys instead of using Google-managed encryption keys.", "severity": "", "tags": {"category": "Compliance", "hipaa": "true", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "plugin": "gcp", "service": "GCP/BigQuery", "soc_2_2017": "true"}, "title": "Ensure that all BigQuery Tables are encrypted with Customer-managed encryption key (CMEK)", "run_status": 8, "run_error": "relation \"gcp_bigquery_table\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.require_bq_table_iam", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "forseti_security_v226": "true", "hipaa": "true", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/BigQuery", "severity": "high", "soc_2_2017": "true"}, "title": "Check if BigQuery datasets are publicly readable", "run_status": 8, "run_error": "relation \"gcp_bigquery_dataset\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.restrict_gmail_bigquery_dataset", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "forseti_security_v226": "true", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/BigQuery", "severity": "high"}, "title": "Enforce corporate domain by banning gmail.com addresses access to BigQuery datasets", "run_status": 8, "run_error": "relation \"gcp_bigquery_dataset\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.restrict_googlegroups_bigquery_dataset", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "forseti_security_v226": "true", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/BigQuery", "severity": "high"}, "title": "Enforce corporate domain by banning googlegroups.com addresses access to BigQuery datasets", "run_status": 8, "run_error": "relation \"gcp_bigquery_dataset\" does not exist (SQLSTATE 42P01)"}]}, {"group_id": "gcp_compliance.benchmark.all_controls_cloudfunction", "title": "Cloud Functions", "description": "This section contains recommendations for configuring Cloud Functions resources.", "tags": {"category": "Compliance", "plugin": "gcp", "service": "GCP/CloudFunctions", "type": "Benchmark"}, "summary": {"status": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 6}}, "groups": [], "controls": [{"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cloudfunction_function_no_deployments_manager_permission", "description": "This control ensures that Cloudfunction function does not allow deployments manager permissions.", "severity": "", "tags": {"category": "Compliance", "plugin": "gcp", "service": "GCP/CloudFunctions"}, "title": "Cloudfunction functions should restrict deployments manager permission", "run_status": 8, "run_error": "relation \"gcp_iam_role\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cloudfunction_function_no_disrupt_logging_permission", "description": "This control ensures that Cloudfunction function does not disrupt logging permissions.", "severity": "", "tags": {"category": "Compliance", "plugin": "gcp", "service": "GCP/CloudFunctions"}, "title": "Cloudfunction functions should restrict disrupt logging permission", "run_status": 8, "run_error": "relation \"gcp_iam_role\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cloudfunction_function_no_ingress_settings_allow_all", "description": "It is recommended that Cloudfunction functions ingress settings should not be set to `allow all` as it allow all inbound requests to the function.", "severity": "", "tags": {"category": "Compliance", "plugin": "gcp", "service": "GCP/CloudFunctions"}, "title": "Cloudfunction functions ingress settings should not be set to allow all", "run_status": 8, "run_error": "relation \"gcp_cloudfunctions_function\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cloudfunction_function_restrict_public_access", "description": "This control ensures that Cloudfunction function is not publicly accessible.", "severity": "", "tags": {"category": "Compliance", "plugin": "gcp", "service": "GCP/CloudFunctions"}, "title": "Cloudfunction functions should restrict public access", "run_status": 8, "run_error": "relation \"gcp_cloudfunctions_function\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cloudfunction_function_restricted_permission", "description": "It is recommended that Cloudfunction functions should not have roles/editor or roles/owner permission.", "severity": "", "tags": {"category": "Compliance", "plugin": "gcp", "service": "GCP/CloudFunctions"}, "title": "Cloudfunction functions no roles/editor or roles/owner permission", "run_status": 8, "run_error": "relation \"gcp_iam_policy\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cloudfunction_function_vpc_connector_enabled", "description": "It is recommended that Cloudfunction functions VPC connector is enabled.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/CloudFunctions"}, "title": "Cloudfunction functions VPC connector should be enabled", "run_status": 8, "run_error": "relation \"gcp_cloudfunctions_function\" does not exist (SQLSTATE 42P01)"}]}, {"group_id": "gcp_compliance.benchmark.all_controls_cloudrun", "title": "Cloud Run", "description": "This section contains recommendations for configuring Cloud Run resources.", "tags": {"category": "Compliance", "plugin": "gcp", "service": "GCP/CloudRun", "type": "Benchmark"}, "summary": {"status": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}}, "groups": [], "controls": [{"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cloudrun_service_restrict_public_access", "description": "This control ensures that Cloud Run service is not publicly accessible.", "severity": "", "tags": {"category": "Compliance", "plugin": "gcp", "service": "GCP/CloudRun"}, "title": "Cloud Run service should restrict public access", "run_status": 8, "run_error": "relation \"gcp_cloud_run_service\" does not exist (SQLSTATE 42P01)"}]}, {"group_id": "gcp_compliance.benchmark.all_controls_compute", "title": "Compute", "description": "This section contains recommendations for configuring Compute resources.", "tags": {"category": "Compliance", "plugin": "gcp", "service": "GCP/Compute", "type": "Benchmark"}, "summary": {"status": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 73}}, "groups": [], "controls": [{"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_backend_bucket_no_dangling_storage_bucket", "description": "This control ensures that Compute Backend Bucket does not have dangling storage bucket.", "severity": "", "tags": {"category": "Compliance", "plugin": "gcp", "service": "GCP/Compute"}, "title": "Compute Backend Bucket should not have dangling storage bucket", "run_status": 8, "run_error": "relation \"gcp_compute_backend_bucket\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_disk_encrypted_with_csk", "description": "Customer-Supplied Encryption Keys (CSEK) are a feature in Google Cloud Storage and Google Compute Engine. If you supply your own encryption keys, Google uses your key to protect the Google-generated keys used to encrypt and decrypt your data. By default, Google Compute Engine encrypts all data at rest. Compute Engine handles and manages this encryption for you without any additional actions on your part. However, if you wanted to control and manage this encryption yourself, you can provide your own encryption keys.", "severity": "", "tags": {"category": "Compliance", "hipaa": "true", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/Compute", "soc_2_2017": "true"}, "title": "Ensure VM disks for critical VMs are encrypted with Customer-Supplied Encryption Keys (CSEK)", "run_status": 8, "run_error": "relation \"gcp_compute_disk\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_external_backend_service_iap_enabled", "description": "This control ensures that external backend service has IAP enabled.", "severity": "", "tags": {"category": "Compliance", "plugin": "gcp", "service": "GCP/Compute"}, "title": "Ensure external backend service has IAP enabled", "run_status": 8, "run_error": "relation \"gcp_compute_backend_service\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_firewall_allow_connections_proxied_by_iap", "description": "Access to VMs should be restricted by firewall rules that allow only IAP traffic by ensuring only connections proxied by the IAP are allowed. To ensure that load balancing works correctly health checks should also be allowed.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/Compute"}, "title": "Ensure Firewall Rules for instances behind Identity Aware Proxy (IAP) only allow the traffic from Google Cloud Loadbalancer (GCLB) Health Check and Proxy Addresses", "run_status": 8, "run_error": "relation \"gcp_compute_firewall\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_firewall_allow_tcp_connections_proxied_by_iap", "description": "IAP authenticates the user requests to your apps via a Google single sign in. You can then manage these users with permissions to control access. It is recommended to use both IAP permissions and firewalls to restrict this access to your apps with sensitive information.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/Compute"}, "title": "Use Identity Aware Proxy (IAP) to Ensure Only Traffic From Google IP Addresses are 'Allowed'", "run_status": 8, "run_error": "relation \"gcp_compute_firewall\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_firewall_default_rule_restrict_ingress_access_except_http_and_https", "description": "This control ensures that default firewall rules does not allow ingress from 0.0.0.0/0 to any port. This is not applicable to default HTTP and HTTPS firewall rule.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/Compute"}, "title": "Ensure no open default firewall rules allow ingress from 0.0.0.0/0 to any port", "run_status": 8, "run_error": "relation \"gcp_compute_firewall\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_firewall_rule_ingress_access_restricted_to_dns_port_53", "description": "Firewall rules provide stateful filtering of ingress/egress network traffic to AWS resources. It is recommended that no security group allows unrestricted ingress access to DNS port 53.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Compute"}, "title": "Ensure no open firewall rules allow ingress from 0.0.0.0/0 to DNS port 53", "run_status": 8, "run_error": "relation \"gcp_compute_firewall\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_firewall_rule_ingress_access_restricted_to_ftp_port_21", "description": "Firewall rules provide stateful filtering of ingress/egress network traffic to AWS resources. It is recommended that no security group allows unrestricted ingress access to FTP port 21.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Compute"}, "title": "Ensure no open firewall rules allow ingress from 0.0.0.0/0 to FTP port 21", "run_status": 8, "run_error": "relation \"gcp_compute_firewall\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_firewall_rule_ingress_access_restricted_to_http_port_80", "description": "Firewall rules provide stateful filtering of ingress/egress network traffic to AWS resources. It is recommended that no security group allows unrestricted ingress access to HTTP port 80.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Compute"}, "title": "Ensure no open firewall rules allow ingress from 0.0.0.0/0 to HTTP port 80", "run_status": 8, "run_error": "relation \"gcp_compute_firewall\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_firewall_rule_ingress_access_restricted_to_microsoft_ds_port_445", "description": "Firewall rules provide stateful filtering of ingress/egress network traffic to AWS resources. It is recommended that no security group allows unrestricted ingress access to Microsoft DS port 445.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Compute"}, "title": "Ensure no open firewall rules allow ingress from 0.0.0.0/0 to Microsoft DS port 445", "run_status": 8, "run_error": "relation \"gcp_compute_firewall\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_firewall_rule_ingress_access_restricted_to_mongo_db_port_27017", "description": "Firewall rules provide stateful filtering of ingress/egress network traffic to AWS resources. It is recommended that no security group allows unrestricted ingress access to MongoDB port 27017.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/Compute"}, "title": "Ensure no open firewall rules allow ingress from 0.0.0.0/0 to MongoDB port 27017", "run_status": 8, "run_error": "relation \"gcp_compute_firewall\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_firewall_rule_ingress_access_restricted_to_mysql_db_port_3306", "description": "Firewall rules provide stateful filtering of ingress/egress network traffic to AWS resources. It is recommended that no security group allows unrestricted ingress access to MySQL DB port 3306.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Compute"}, "title": "Ensure no open firewall rules allow ingress from 0.0.0.0/0 to MySQL DB port 3306", "run_status": 8, "run_error": "relation \"gcp_compute_firewall\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_firewall_rule_ingress_access_restricted_to_netbios_snn_port_139", "description": "Firewall rules provide stateful filtering of ingress/egress network traffic to AWS resources. It is recommended that no security group allows unrestricted ingress access to NetBIOS SSN port 139.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/Compute"}, "title": "Ensure no open firewall rules allow ingress from 0.0.0.0/0 to NetBIOS SSN port 139", "run_status": 8, "run_error": "relation \"gcp_compute_firewall\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_firewall_rule_ingress_access_restricted_to_oracle_db_port_1521", "description": "Firewall rules provide stateful filtering of ingress/egress network traffic to AWS resources. It is recommended that no security group allows unrestricted ingress access to Oracle DB port 1521.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Compute"}, "title": "Ensure no open firewall rules allow ingress from 0.0.0.0/0 to Oracle DB port 1521", "run_status": 8, "run_error": "relation \"gcp_compute_firewall\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_firewall_rule_ingress_access_restricted_to_pop3_port_110", "description": "Firewall rules provide stateful filtering of ingress/egress network traffic to AWS resources. It is recommended that no security group allows unrestricted ingress access to POP3 port 110.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Compute"}, "title": "Ensure no open firewall rules allow ingress from 0.0.0.0/0 to POP3 port 110", "run_status": 8, "run_error": "relation \"gcp_compute_firewall\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_firewall_rule_ingress_access_restricted_to_postgresql_port_10250", "description": "Firewall rules provide stateful filtering of ingress/egress network traffic to AWS resources. It is recommended that no security group allows unrestricted ingress access to port 10250.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/Compute"}, "title": "Ensure no open firewall rules allow ingress from 0.0.0.0/0 to port 10250", "run_status": 8, "run_error": "relation \"gcp_compute_firewall\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_firewall_rule_ingress_access_restricted_to_postgresql_port_10255", "description": "Firewall rules provide stateful filtering of ingress/egress network traffic to AWS resources. It is recommended that no security group allows unrestricted ingress access to port 10255.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/Compute"}, "title": "Ensure no open firewall rules allow ingress from 0.0.0.0/0 to port 10255", "run_status": 8, "run_error": "relation \"gcp_compute_firewall\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_firewall_rule_ingress_access_restricted_to_postgresql_port_5432", "description": "Firewall rules provide stateful filtering of ingress/egress network traffic to AWS resources. It is recommended that no security group allows unrestricted ingress access to PostgreSQL port 5432.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Compute"}, "title": "Ensure no open firewall rules allow ingress from 0.0.0.0/0 to PostgreSQL port 5432", "run_status": 8, "run_error": "relation \"gcp_compute_firewall\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_firewall_rule_ingress_access_restricted_to_smtp_port_25", "description": "Firewall rules provide stateful filtering of ingress/egress network traffic to AWS resources. It is recommended that no security group allows unrestricted ingress access to SMTP port 25.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Compute"}, "title": "Ensure no open firewall rules allow ingress from 0.0.0.0/0 to SMTP port 25", "run_status": 8, "run_error": "relation \"gcp_compute_firewall\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_firewall_rule_ingress_access_restricted_to_tcp_port_137_to_139", "description": "Firewall rules provide stateful filtering of ingress/egress network traffic to AWS resources. It is recommended that no security group allows unrestricted ingress access to TCP port 137 to 139.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Compute"}, "title": "Ensure no open firewall rules allow ingress from 0.0.0.0/0 to port TCP 137 to 139", "run_status": 8, "run_error": "relation \"gcp_compute_firewall\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_firewall_rule_ingress_access_restricted_to_tcp_port_27017_to_27019", "description": "Firewall rules provide stateful filtering of ingress/egress network traffic to AWS resources. It is recommended that no security group allows unrestricted ingress access to TCP port 27017 to 27019.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Compute"}, "title": "Ensure no open firewall rules allow ingress from 0.0.0.0/0 to port TCP 27017 to 27019", "run_status": 8, "run_error": "relation \"gcp_compute_firewall\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_firewall_rule_ingress_access_restricted_to_tcp_port_61620_61621", "description": "Firewall rules provide stateful filtering of ingress/egress network traffic to AWS resources. It is recommended that no security group allows unrestricted ingress access to TCP port 61620 or 6162.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Compute"}, "title": "Ensure no open firewall rules allow ingress from 0.0.0.0/0 to port TCP 61620 or 6162", "run_status": 8, "run_error": "relation \"gcp_compute_firewall\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_firewall_rule_ingress_access_restricted_to_tcp_port_636", "description": "Firewall rules provide stateful filtering of ingress/egress network traffic to AWS resources. It is recommended that no security group allows unrestricted ingress access to TCP port 636.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Compute"}, "title": "Ensure no open firewall rules allow ingress from 0.0.0.0/0 to port TCP 636", "run_status": 8, "run_error": "relation \"gcp_compute_firewall\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_firewall_rule_ingress_access_restricted_to_tcp_port_6379", "description": "Firewall rules provide stateful filtering of ingress/egress network traffic to AWS resources. It is recommended that no security group allows unrestricted ingress access to TCP port 6379.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Compute"}, "title": "Ensure no open firewall rules allow ingress from 0.0.0.0/0 to port TCP 6379", "run_status": 8, "run_error": "relation \"gcp_compute_firewall\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_firewall_rule_ingress_access_restricted_to_tcp_port_7000_7001", "description": "Firewall rules provide stateful filtering of ingress/egress network traffic to AWS resources. It is recommended that no security group allows unrestricted ingress access to TCP port 7000 or 7001.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Compute"}, "title": "Ensure no open firewall rules allow ingress from 0.0.0.0/0 to port TCP 7000 or 7001", "run_status": 8, "run_error": "relation \"gcp_compute_firewall\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_firewall_rule_ingress_access_restricted_to_tcp_port_7199", "description": "Firewall rules provide stateful filtering of ingress/egress network traffic to AWS resources. It is recommended that no security group allows unrestricted ingress access to TCP port 7199.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Compute"}, "title": "Ensure no open firewall rules allow ingress from 0.0.0.0/0 to port TCP 7199", "run_status": 8, "run_error": "relation \"gcp_compute_firewall\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_firewall_rule_ingress_access_restricted_to_tcp_port_8888", "description": "Firewall rules provide stateful filtering of ingress/egress network traffic to AWS resources. It is recommended that no security group allows unrestricted ingress access to TCP port 8888.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Compute"}, "title": "Ensure no open firewall rules allow ingress from 0.0.0.0/0 to port TCP 8888", "run_status": 8, "run_error": "relation \"gcp_compute_firewall\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_firewall_rule_ingress_access_restricted_to_tcp_port_9042", "description": "Firewall rules provide stateful filtering of ingress/egress network traffic to AWS resources. It is recommended that no security group allows unrestricted ingress access to TCP port 9042.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Compute"}, "title": "Ensure no open firewall rules allow ingress from 0.0.0.0/0 to port TCP 9042", "run_status": 8, "run_error": "relation \"gcp_compute_firewall\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_firewall_rule_ingress_access_restricted_to_tcp_port_9090", "description": "Firewall rules provide stateful filtering of ingress/egress network traffic to AWS resources. It is recommended that no security group allows unrestricted ingress access to TCP port 9090.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Compute"}, "title": "Ensure no open firewall rules allow ingress from 0.0.0.0/0 to port TCP 9090", "run_status": 8, "run_error": "relation \"gcp_compute_firewall\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_firewall_rule_ingress_access_restricted_to_tcp_port_9160", "description": "Firewall rules provide stateful filtering of ingress/egress network traffic to AWS resources. It is recommended that no security group allows unrestricted ingress access to TCP port 9160.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Compute"}, "title": "Ensure no open firewall rules allow ingress from 0.0.0.0/0 to port TCP 9160", "run_status": 8, "run_error": "relation \"gcp_compute_firewall\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_firewall_rule_ingress_access_restricted_to_tcp_port_9200_9300", "description": "Firewall rules provide stateful filtering of ingress/egress network traffic to AWS resources. It is recommended that no security group allows unrestricted ingress access to TCP port 9200 or 9300.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Compute"}, "title": "Ensure no open firewall rules allow ingress from 0.0.0.0/0 to port TCP 9200 or 9300", "run_status": 8, "run_error": "relation \"gcp_compute_firewall\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_firewall_rule_ingress_access_restricted_to_tcp_udp_port_11211", "description": "Firewall rules provide stateful filtering of ingress/egress network traffic to AWS resources. It is recommended that no security group allows unrestricted ingress access to TCP or UDP port 11211.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Compute"}, "title": "Ensure no open firewall rules allow ingress from 0.0.0.0/0 to TCP or UDP port 11211", "run_status": 8, "run_error": "relation \"gcp_compute_firewall\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_firewall_rule_ingress_access_restricted_to_tcp_udp_port_11214_to_11215", "description": "Firewall rules provide stateful filtering of ingress/egress network traffic to AWS resources. It is recommended that no security group allows unrestricted ingress access to TCP or UDP port 11214 to 11215.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Compute"}, "title": "Ensure no open firewall rules allow ingress from 0.0.0.0/0 to TCP or UDP port 11214 to 11215", "run_status": 8, "run_error": "relation \"gcp_compute_firewall\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_firewall_rule_ingress_access_restricted_to_tcp_udp_port_2483_to_2484", "description": "Firewall rules provide stateful filtering of ingress/egress network traffic to AWS resources. It is recommended that no security group allows unrestricted ingress access to TCP or UDP port 2483 to 2484.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Compute"}, "title": "Ensure no open firewall rules allow ingress from 0.0.0.0/0 to TCP or UDP port 2483 to 24845", "run_status": 8, "run_error": "relation \"gcp_compute_firewall\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_firewall_rule_ingress_access_restricted_to_tcp_udp_port_389", "description": "Firewall rules provide stateful filtering of ingress/egress network traffic to AWS resources. It is recommended that no security group allows unrestricted ingress access to TCP or UDP port 389.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Compute"}, "title": "Ensure no open firewall rules allow ingress from 0.0.0.0/0 to TCP or UDP port 389", "run_status": 8, "run_error": "relation \"gcp_compute_firewall\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_firewall_rule_ingress_access_restricted_to_telnet_port_23", "description": "Firewall rules provide stateful filtering of ingress/egress network traffic to AWS resources. It is recommended that no security group allows unrestricted ingress access to Telnet port 23.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Compute"}, "title": "Ensure no open firewall rules allow ingress from 0.0.0.0/0 to Telnet port 23", "run_status": 8, "run_error": "relation \"gcp_compute_firewall\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_firewall_rule_logging_enabled", "description": "Firewall rules should have logging enabled. This control fails if logging is disabled for firewall rule.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Compute"}, "title": "Ensure compute firewall rule have logging enabled", "run_status": 8, "run_error": "relation \"gcp_compute_firewall\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_firewall_rule_restrict_ingress_all_with_no_specific_target", "description": "Firewall rules provide stateful filtering of ingress/egress network traffic to AWS resources. It is recommended that no security group allows unrestricted ingress access to any port without any specific target.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Compute"}, "title": "Ensure no open firewall rules allow ingress from 0.0.0.0/0 to any port without any specific target", "run_status": 8, "run_error": "relation \"gcp_compute_firewall\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_firewall_rule_restrict_ingress_all", "description": "Firewall rules provide stateful filtering of ingress/egress network traffic to AWS resources. It is recommended that no security group allows unrestricted ingress access to any port.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Compute"}, "title": "Ensure no open firewall rules allow ingress from 0.0.0.0/0 to any port", "run_status": 8, "run_error": "relation \"gcp_compute_firewall\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_https_load_balancer_logging_enabled", "description": "Logging enabled on a HTTPS Load Balancer will show all network traffic and its destination.", "severity": "", "tags": {"category": "Compliance", "hipaa": "true", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/Compute"}, "title": "Ensure Logging is enabled for HTTP(S) Load Balancer", "run_status": 8, "run_error": "relation \"gcp_compute_url_map\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_instance_block_project_wide_ssh_enabled", "description": "It is recommended to use Instance specific SSH key(s) instead of using common/shared project-wide SSH key(s) to access Instances.", "severity": "", "tags": {"category": "Compliance", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/Compute", "soc_2_2017": "true"}, "title": "Ensure 'Block Project-wide SSH keys' is enabled for VM instances", "run_status": 8, "run_error": "relation \"gcp_compute_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_instance_confidential_computing_enabled", "description": "Google Cloud encrypts data at-rest and in-transit, but customer data must be decrypted for processing. Confidential Computing is a breakthrough technology which encrypts data in-use—while it is being processed. Confidential Computing environments keep data encrypted in memory and elsewhere outside the central processing unit (CPU).", "severity": "", "tags": {"category": "Compliance", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/Compute", "soc_2_2017": "true"}, "title": "Ensure that Compute instances have Confidential Computing enabled", "run_status": 8, "run_error": "relation \"gcp_compute_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_instance_ip_forwarding_disabled", "description": "Compute Engine instance cannot forward a packet unless the source IP address of the packet matches the IP address of the instance. Similarly, GCP won't deliver a packet whose destination IP address is different than the IP address of the instance receiving the packet. However, both capabilities are required if you want to use instances to help route packets.", "severity": "", "tags": {"category": "Compliance", "nist_800_53_rev_5": "true", "plugin": "gcp", "service": "GCP/Compute", "soc_2_2017": "true"}, "title": "Ensure that IP forwarding is not enabled on Instances", "run_status": 8, "run_error": "relation \"gcp_compute_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_instance_no_data_destruction_permission", "description": "This control ensures that Compute Instance does not allow data destruction permissions.", "severity": "", "tags": {"category": "Compliance", "plugin": "gcp", "service": "GCP/Compute"}, "title": "Compute Instances should restrict data destruction permission", "run_status": 8, "run_error": "relation \"gcp_iam_role\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_instance_no_database_write_permission", "description": "This control ensures that Compute Instance does not allow database write permissions.", "severity": "", "tags": {"category": "Compliance", "plugin": "gcp", "service": "GCP/Compute"}, "title": "Compute Instances should restrict database write permission", "run_status": 8, "run_error": "relation \"gcp_iam_role\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_instance_no_deployments_manager_permission", "description": "This control ensures that Compute Instance does not allow deployments manager permissions.", "severity": "", "tags": {"category": "Compliance", "plugin": "gcp", "service": "GCP/Compute"}, "title": "Compute Instances should restrict deployments manager permission", "run_status": 8, "run_error": "relation \"gcp_iam_role\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_instance_no_disrupt_logging_permission", "description": "This control ensures that Compute Instance does not disrupt logging permissions.", "severity": "", "tags": {"category": "Compliance", "plugin": "gcp", "service": "GCP/Compute"}, "title": "Compute Instances should restrict disrupt logging permission", "run_status": 8, "run_error": "relation \"gcp_iam_role\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_instance_no_iam_write_permission", "description": "This is control ensures that Compute Instance does not allow IAM write permissions.", "severity": "", "tags": {"category": "Compliance", "plugin": "gcp", "service": "GCP/Compute"}, "title": "Compute Instances should restrict IAM write permission", "run_status": 8, "run_error": "relation \"gcp_iam_role\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_instance_no_service_account_impersonate_permission", "description": "This control ensures that Compute Instance does not allow service account impersonate permissions.", "severity": "", "tags": {"category": "Compliance", "plugin": "gcp", "service": "GCP/Compute"}, "title": "Compute Instances should restrict service account impersonate permission", "run_status": 8, "run_error": "relation \"gcp_iam_role\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_instance_no_write_permission_on_deny_policy", "description": "This control ensures that Compute Instance does not allow write permission on deny policies.", "severity": "", "tags": {"category": "Compliance", "plugin": "gcp", "service": "GCP/Compute"}, "title": "Compute Instances should restrict write permission on deny policy", "run_status": 8, "run_error": "relation \"gcp_iam_role\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_instance_oslogin_enabled", "description": "Enabling OS login binds SSH certificates to IAM users and facilitates effective SSH certificate management.", "severity": "", "tags": {"category": "Compliance", "nist_800_53_rev_5": "true", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/Compute", "soc_2_2017": "true"}, "title": "Ensure OS login is enabled for all instances in the Project", "run_status": 8, "run_error": "relation \"gcp_compute_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_instance_preemptible_termination_disabled", "description": "This control ensures that Compute Instance preemptible termination is disabled. Compute Instance preemptible termination can lead to unexpected loss of service when the VM instance is terminated.", "severity": "", "tags": {"category": "Compliance", "plugin": "gcp", "service": "GCP/Compute"}, "title": "Compute Instance preemptible termination should be disabled", "run_status": 8, "run_error": "relation \"gcp_compute_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_instance_serial_port_connection_disabled", "description": "Interacting with a serial port is often referred to as the serial console, which is similar to using a terminal window, in that input and output is entirely in text mode and there is no graphical interface or mouse support.", "severity": "", "tags": {"category": "Compliance", "nist_800_53_rev_5": "true", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/Compute", "soc_2_2017": "true"}, "title": "Ensure 'Enable connecting to serial ports' is not enabled for VM Instance", "run_status": 8, "run_error": "relation \"gcp_compute_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_instance_shielded_vm_enabled", "description": "To defend against advanced threats and ensure that the boot loader and firmware on your VMs are signed and untampered, it is recommended that Compute instances are launched with Shielded VM enabled.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/Compute"}, "title": "Ensure Compute instances are launched with Shielded VM enabled", "run_status": 8, "run_error": "relation \"gcp_compute_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_instance_template_ip_forwarding_disabled", "description": "Compute Engine instance template cannot forward a packet unless the source IP address of the packet matches the IP address of the instance. Similarly, GCP won't deliver a packet whose destination IP address is different than the IP address of the instance receiving the packet. However, both capabilities are required if you want to use instances to help route packets.", "severity": "", "tags": {"category": "Compliance", "plugin": "gcp", "service": "GCP/Compute"}, "title": "Compute Instance template IP forwarding should be disabled", "run_status": 8, "run_error": "relation \"gcp_compute_instance_template\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_instance_with_custom_metadata", "description": "This control ensures that Compute Instance have custom metadata. Custom metadata facilitates simple identification and enhances searchability.", "severity": "", "tags": {"category": "Compliance", "plugin": "gcp", "service": "GCP/Compute"}, "title": "Compute Instances should have custom metadata", "run_status": 8, "run_error": "relation \"gcp_compute_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_instance_with_no_default_service_account_with_full_access", "description": "To support principle of least privileges and prevent potential privilege escalation it is recommended that instances are not assigned to default service account Compute Engine default service account with Scope Allow full access to all Cloud APIs.", "severity": "", "tags": {"category": "Compliance", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Compute", "soc_2_2017": "true"}, "title": "Ensure that instances are not configured to use the default service account with full access to all Cloud APIs", "run_status": 8, "run_error": "relation \"gcp_compute_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_instance_with_no_default_service_account", "description": "It is recommended to configure your instance to not use the default Compute Engine service account because it has the Editor role on the project.", "severity": "", "tags": {"category": "Compliance", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "nist_csf_v2": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Compute", "soc_2_2017": "true"}, "title": "Ensure that instances are not configured to use the default service account", "run_status": 8, "run_error": "relation \"gcp_compute_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_instance_with_no_public_ip_addresses", "description": "Compute instances should not be configured to have external IP addresses.", "severity": "", "tags": {"category": "Compliance", "hipaa": "true", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "nist_csf_v2": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Compute", "soc_2_2017": "true"}, "title": "Ensure that Compute instances do not have public IP addresses", "run_status": 8, "run_error": "relation \"gcp_compute_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_instance_wth_no_high_level_basic_role", "description": "This control ensures that Compute Instance does not allow high level basic role.", "severity": "", "tags": {"category": "Compliance", "plugin": "gcp", "service": "GCP/Compute"}, "title": "Compute Instances should restrict high level basic role", "run_status": 8, "run_error": "relation \"gcp_iam_policy\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_network_auto_create_subnetwork_enabled", "description": "This control ensures that auto create subnetwork is enabled for Compute Network. Legacy network is not recommended, subnetworks cannot be created in a legacy network.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/Compute"}, "title": "Compute Networks should have auto create subnetwork enabled", "run_status": 8, "run_error": "relation \"gcp_compute_network\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_network_contains_no_default_network", "description": "To prevent the use of default network, a project should not have a default network.", "severity": "", "tags": {"category": "Compliance", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/Compute", "soc_2_2017": "true"}, "title": "Ensure that the default network does not exist in a project", "run_status": 8, "run_error": "relation \"gcp_compute_network\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_network_contains_no_legacy_network", "description": "In order to prevent use of legacy networks, a project should not have a legacy network configured.", "severity": "", "tags": {"category": "Compliance", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/Compute", "soc_2_2017": "true"}, "title": "Ensure legacy networks do not exist for a project", "run_status": 8, "run_error": "relation \"gcp_compute_network\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_network_dns_logging_enabled", "description": "Cloud DNS logging records the queries from the name servers within your VPC to Stackdriver. Logged queries can come from Compute Engine VMs, GKE containers, or other GCP resources provisioned within the VPC.", "severity": "", "tags": {"category": "Compliance", "hipaa": "true", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/Compute", "soc_2_2017": "true"}, "title": "Ensure that Cloud DNS logging is enabled for all VPC networks", "run_status": 8, "run_error": "relation \"gcp_dns_policy\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_ssl_policy_with_no_weak_cipher", "description": "Secure Sockets Layer (SSL) policies determine what port Transport Layer Security (TLS) features clients are permitted to use when connecting to load balancers.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Compute"}, "title": "Ensure no HTTPS or SSL proxy load balancers permit SSL policies with weak cipher suites", "run_status": 8, "run_error": "relation \"gcp_compute_target_ssl_proxy\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_target_https_proxy_quic_protocol_enabled", "description": "This control ensures that Compute Target HTTPS proxy QUIC protocol is enabled. Activating the QUIC protocol in load balancer target HTTPS proxies offers the benefit of quicker connection establishment.", "severity": "", "tags": {"category": "Compliance", "plugin": "gcp", "service": "GCP/Compute"}, "title": "Compute Target HTTPS proxy QUIC protocol should be enabled", "run_status": 8, "run_error": "relation \"gcp_compute_target_https_proxy\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_target_https_proxy_quic_protocol_no_default_ssl_policy", "description": "This control ensures that Compute Target HTTPS proxy should use custom SSL policy.", "severity": "", "tags": {"category": "Compliance", "plugin": "gcp", "service": "GCP/Compute"}, "title": "Compute Target HTTPS proxy should use custom SSL policy", "run_status": 8, "run_error": "relation \"gcp_compute_target_https_proxy\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.compute_target_https_uses_latest_tls_version", "description": "This control ensures that HTTP target use latest TLS version.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/Compute"}, "title": "Ensure HTTPS target use latest TLS version", "run_status": 8, "run_error": "relation \"gcp_compute_target_https_proxy\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.enable_network_flow_logs", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "nist_csf_v2": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Compute", "severity": "high", "soc_2_2017": "true"}, "title": "Ensure VPC Flow logs is enabled for every subnet in VPC Network", "run_status": 8, "run_error": "relation \"gcp_compute_subnetwork\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.enable_network_private_google_access", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/Compute", "severity": "high"}, "title": "Ensure Private Google Access is enabled for all subnetworks in VPC", "run_status": 8, "run_error": "relation \"gcp_compute_subnetwork\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.restrict_firewall_rule_rdp_world_open", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "nist_800_53_rev_5": "true", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/Compute", "severity": "high", "soc_2_2017": "true"}, "title": "Check for open firewall rules allowing RDP from the internet", "run_status": 8, "run_error": "relation \"gcp_compute_firewall\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.restrict_firewall_rule_ssh_world_open", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "forseti_security_v226": "true", "nist_800_53_rev_5": "true", "nist_csf_v2": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Compute", "severity": "high", "soc_2_2017": "true"}, "title": "Check for open firewall rules allowing SSH from the internet", "run_status": 8, "run_error": "relation \"gcp_compute_firewall\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.restrict_firewall_rule_world_open_tcp_udp_all_ports", "description": "", "severity": "", "tags": {"category": "Compliance", "forseti_security_v226": "true", "nist_csf_v2": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Compute", "severity": "high"}, "title": "Check for open firewall rules allowing TCP/UDP from the internet", "run_status": 8, "run_error": "relation \"gcp_compute_firewall\" does not exist (SQLSTATE 42P01)"}]}, {"group_id": "gcp_compliance.benchmark.all_controls_dataproc", "title": "Dataproc", "description": "This section contains recommendations for configuring Dataproc resources.", "tags": {"category": "Compliance", "plugin": "gcp", "service": "GCP/Dataproc", "type": "Benchmark"}, "summary": {"status": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}}, "groups": [], "controls": [{"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.dataproc_cluster_encryption_with_cmek", "description": "When you use Dataproc, cluster, and job data is stored on Persistent Disks (PDs) associated with the Compute Engine VMs in your cluster and in a Cloud Storage staging bucket. This PD and bucket data is encrypted using a Google-generated data encryption key (DEK) and key encryption key (KEK). The CMEK feature allows you to create, use, and revoke the key-encryption key (KEK). Google still controls the data encryption key (DEK).", "severity": "", "tags": {"category": "Compliance", "hipaa": "true", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/Dataproc", "soc_2_2017": "true"}, "title": "Ensure that dataproc cluster is encrypted using customer-managed encryption key", "run_status": 8, "run_error": "relation \"gcp_dataproc_cluster\" does not exist (SQLSTATE 42P01)"}]}, {"group_id": "gcp_compliance.benchmark.all_controls_dns", "title": "DNS", "description": "This section contains recommendations for configuring DNS resources.", "tags": {"category": "Compliance", "plugin": "gcp", "service": "GCP/DNS", "type": "Benchmark"}, "summary": {"status": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 3}}, "groups": [], "controls": [{"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.dns_managed_zone_dnssec_enabled", "description": "Cloud Domain Name System (DNS) is a fast, reliable, and cost-effective domain name system that powers millions of domains on the internet. Domain Name System Security Extensions (DNSSEC) in Cloud DNS enables domain owners to take easy steps to protect their domains against DNS hijacking and man-in-the-middle and other attacks.", "severity": "", "tags": {"category": "Compliance", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "plugin": "gcp", "service": "GCP/DNS", "soc_2_2017": "true"}, "title": "Ensure that DNSSEC is enabled for Cloud DNS", "run_status": 8, "run_error": "relation \"gcp_dns_managed_zone\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.dnssec_prevent_rsasha1_ksk", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "plugin": "gcp", "service": "GCP/DNS", "severity": "high", "soc_2_2017": "true"}, "title": "Ensure that RSASHA1 is not used for key-signing key in Cloud DNS", "run_status": 8, "run_error": "relation \"gcp_dns_managed_zone\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.dnssec_prevent_rsasha1_zsk", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "plugin": "gcp", "service": "GCP/DNS", "severity": "high"}, "title": "Ensure that RSASHA1 is not used for zone-signing key in Cloud DNS", "run_status": 8, "run_error": "relation \"gcp_dns_managed_zone\" does not exist (SQLSTATE 42P01)"}]}, {"group_id": "gcp_compliance.benchmark.all_controls_iam", "title": "IAM", "description": "This section contains recommendations for configuring IAM resources.", "tags": {"category": "Compliance", "plugin": "gcp", "service": "GCP/IAM", "type": "Benchmark"}, "summary": {"status": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 12}}, "groups": [], "controls": [{"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.denylist_public_users", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "forseti_security_v226": "true", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/IAM", "severity": "high"}, "title": "Prevent public users from having access to resources via IAM", "run_status": 8, "run_error": "relation \"gcp_iam_policy\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.iam_api_key_age_90", "description": "It is recommended to rotate API keys every 90 days.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v10": "true", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/IAM"}, "title": "Ensure API keys are rotated every 90 days", "run_status": 8, "run_error": "relation \"gcp_apikeys_key\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.iam_api_key_restricts_apis", "description": "API keys are insecure because they can be viewed publicly, such as from within a browser, or they can be accessed on a device where the key resides. It is recommended to restrict API keys to use (call) only APIs required by an application.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v10": "true", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/IAM"}, "title": "Ensure API keys are restricted to only APIs that application needs access", "run_status": 8, "run_error": "relation \"gcp_apikeys_key\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.iam_api_key_restricts_websites_hosts_apps", "description": "Unrestricted keys are insecure because they can be viewed publicly, such as from within a browser, or they can be accessed on a device where the key resides. It is recommended to restrict API key usage to trusted hosts, HTTP referrers and apps.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/IAM"}, "title": "Ensure API keys are restricted to use by only specified Hosts and Apps", "run_status": 8, "run_error": "relation \"gcp_apikeys_key\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.iam_restrict_service_account_key_age_one_hundred_days", "description": "", "severity": "", "tags": {"category": "Compliance", "forseti_security_v226": "true", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/IAM", "severity": "high"}, "title": "Check if service account keys are older than 100 days", "run_status": 8, "run_error": "relation \"gcp_service_account_key\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.iam_service_account_gcp_managed_key", "description": "User managed service accounts should not have user-managed keys.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/IAM"}, "title": "Ensure that there are only GCP-managed service account keys for each service account", "run_status": 8, "run_error": "relation \"gcp_service_account_key\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.iam_service_account_key_age_90", "description": "Service Account keys consist of a key ID (Private_key_Id) and Private key, which are used to sign programmatic requests users make to Google cloud services accessible to that particular service account. It is recommended that all Service Account keys are regularly rotated.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/IAM"}, "title": "Ensure user-managed/external keys for service accounts are rotated every 90 days or less", "run_status": 8, "run_error": "relation \"gcp_service_account_key\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.iam_service_account_without_admin_privilege", "description": "A service account is a special Google account that belongs to an application or a VM, instead of to an individual end-user. The application uses the service account to call the service's Google API so that users aren't directly involved. It's recommended not to use admin access for ServiceAccount.", "severity": "", "tags": {"category": "Compliance", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/IAM", "soc_2_2017": "true"}, "title": "Ensure that Service Account has no Admin privileges", "run_status": 8, "run_error": "relation \"gcp_iam_policy\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.iam_user_kms_separation_of_duty_enforced", "description": "It is recommended that the principle of 'Separation of Duties' is enforced while assigning KMS related roles to users.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/IAM"}, "title": "Ensure that Separation of duties is enforced while assigning KMS related roles to users", "run_status": 8, "run_error": "relation \"gcp_iam_policy\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.iam_user_not_assigned_service_account_user_role_project_level", "description": "It is recommended to assign the Service Account User (iam.serviceAccountUser) and Service Account Token Creator (iam.serviceAccountTokenCreator) roles to a user for a specific service account rather than assigning the role to a user at project level.", "severity": "", "tags": {"category": "Compliance", "hipaa": "true", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "nist_csf_v2": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/IAM", "soc_2_2017": "true"}, "title": "Ensure that IAM users are not assigned the Service Account User or Service Account Token Creator roles at project level", "run_status": 8, "run_error": "relation \"gcp_iam_policy\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.iam_user_separation_of_duty_enforced", "description": "It is recommended that the principle of 'Separation of Duties' is enforced while assigning service-account related roles to users.", "severity": "", "tags": {"category": "Compliance", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/IAM", "soc_2_2017": "true"}, "title": "Ensure that Separation of duties is enforced while assigning service account related roles to users", "run_status": 8, "run_error": "relation \"gcp_iam_policy\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.only_my_domain", "description": "", "severity": "", "tags": {"category": "Compliance", "forseti_security_v226": "true", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/IAM", "severity": "high"}, "title": "Only allow members from my domain to be added to IAM roles", "run_status": 8, "run_error": "relation \"gcp_iam_policy\" does not exist (SQLSTATE 42P01)"}]}, {"group_id": "gcp_compliance.benchmark.all_controls_kms", "title": "KMS", "description": "This section contains recommendations for configuring KMS resources.", "tags": {"category": "Compliance", "plugin": "gcp", "service": "GCP/KMS", "type": "Benchmark"}, "summary": {"status": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 5}}, "groups": [], "controls": [{"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cmek_rotation_one_hundred_days", "description": "", "severity": "", "tags": {"category": "Compliance", "forseti_security_v226": "true", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/KMS", "severity": "high"}, "title": "Check that CMEK rotation policy is in place and is sufficiently short", "run_status": 8, "run_error": "relation \"gcp_kms_key\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.kms_key_not_publicly_accessible", "description": "It is recommended that the IAM policy on Cloud KMS cryptokeys should restrict anonymous and/or public access.", "severity": "", "tags": {"category": "Compliance", "hipaa": "true", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/KMS", "soc_2_2017": "true"}, "title": "Ensure that Cloud KMS cryptokeys are not anonymously or publicly accessible", "run_status": 8, "run_error": "relation \"gcp_kms_key\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.kms_key_rotated_within_90_day", "description": "Google Cloud Key Management Service stores cryptographic keys in a hierarchical structure designed for useful and elegant access control management. The format for the rotation schedule depends on the client library that is used. For the gcloud command-line tool, the next rotation time must be in ISO or RFC3339 format, and the rotation period must be in the form INTEGER[UNIT], where units can be one of seconds (s), minutes (m), hours (h) or days (d).", "severity": "", "tags": {"category": "Compliance", "hipaa": "true", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "nist_csf_v2": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/KMS", "soc_2_2017": "true"}, "title": "Ensure KMS encryption keys are rotated within a period of 90 days", "run_status": 8, "run_error": "relation \"gcp_kms_key\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.kms_key_separation_of_duties_enforced", "description": "It is recommended that the principle of 'Separation of Duties' is enforced while assigning KMS related roles to users.", "severity": "", "tags": {"category": "Compliance", "hipaa": "true", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/KMS", "soc_2_2017": "true"}, "title": "Ensure that Separation of duties is enforced while assigning KMS related roles to users", "run_status": 8, "run_error": "relation \"gcp_iam_policy\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.kms_key_users_limited_to_3", "description": "It is recommended that KMS encryption keys users should be limited to three.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/KMS"}, "title": "Ensure KMS encryption keys has three or less than three number of users", "run_status": 8, "run_error": "relation \"gcp_kms_key\" does not exist (SQLSTATE 42P01)"}]}, {"group_id": "gcp_compliance.benchmark.all_controls_kubernetes", "title": "Kubernetes", "description": "This section contains recommendations for configuring Kubernetes resources.", "tags": {"category": "Compliance", "plugin": "gcp", "service": "GCP/Kubernetes", "type": "Benchmark"}, "summary": {"status": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 32}}, "groups": [], "controls": [{"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.allow_only_private_cluster", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Kubernetes", "severity": "high"}, "title": "Verify all GKE clusters are Private Clusters", "run_status": 8, "run_error": "relation \"gcp_kubernetes_cluster\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.disable_gke_dashboard", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Kubernetes", "severity": "high"}, "title": "Ensure Kubernetes web UI/Dashboard is disabled", "run_status": 8, "run_error": "relation \"gcp_kubernetes_cluster\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.disable_gke_default_service_account", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "plugin": "gcp", "service": "GCP/Kubernetes", "severity": "high"}, "title": "Ensure default Service account is not used for Project access in Kubernetes Engine clusters", "run_status": 8, "run_error": "relation \"gcp_kubernetes_cluster\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.disable_gke_legacy_abac", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Kubernetes", "severity": "high"}, "title": "Ensure Legacy Authorization is set to Disabled on Kubernetes Engine Clusters", "run_status": 8, "run_error": "relation \"gcp_kubernetes_cluster\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.disable_gke_legacy_endpoints", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "plugin": "gcp", "service": "GCP/Kubernetes", "severity": "high"}, "title": "Check that legacy metadata endpoints are disabled on Kubernetes clusters(disabled by default since GKE 1.12+)", "run_status": 8, "run_error": "relation \"gcp_kubernetes_cluster\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.enable_alias_ip_ranges", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Kubernetes", "severity": "high"}, "title": "Ensure Kubernetes Cluster is created with Alias IP ranges enabled", "run_status": 8, "run_error": "relation \"gcp_kubernetes_cluster\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.enable_auto_repair", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Kubernetes", "severity": "high"}, "title": "Ensure automatic node repair is enabled on all node pools in a GKE cluster", "run_status": 8, "run_error": "relation \"gcp_kubernetes_cluster\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.enable_auto_upgrade", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "nist_csf_v2": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Kubernetes", "severity": "high"}, "title": "Ensure Automatic node upgrades is enabled on Kubernetes Engine Clusters nodes", "run_status": 8, "run_error": "relation \"gcp_kubernetes_cluster\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.enable_gke_master_authorized_networks", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "nist_csf_v2": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Kubernetes", "severity": "high"}, "title": "Ensure Master authorized networks is set to Enabled on Kubernetes Engine Clusters", "run_status": 8, "run_error": "relation \"gcp_kubernetes_cluster\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.gke_container_optimized_os", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Kubernetes", "severity": "high"}, "title": "Ensure Container-Optimized OS (cos) is used for Kubernetes engine clusters", "run_status": 8, "run_error": "relation \"gcp_kubernetes_cluster\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.gke_restrict_pod_traffic", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "plugin": "gcp", "service": "GCP/Kubernetes", "severity": "high"}, "title": "Check that GKE clusters have a Network Policy installed", "run_status": 8, "run_error": "relation \"gcp_kubernetes_cluster\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.kubernetes_cluster_binary_authorization_enabled", "description": "This control ensures that GKE clusters binary authorization is enabled.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/Kubernetes"}, "title": "GKE clusters binary authorization should be enabled", "run_status": 8, "run_error": "relation \"gcp_kubernetes_cluster\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.kubernetes_cluster_client_certificate_authentication_enabled", "description": "This control ensures that GKE clusters client certificate authentication is enabled.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/Kubernetes"}, "title": "GKE clusters client certificate authentication should be enabled", "run_status": 8, "run_error": "relation \"gcp_kubernetes_cluster\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.kubernetes_cluster_database_encryption_enabled", "description": "This control ensures that GKE clusters have database encryption enabled.", "severity": "", "tags": {"category": "Compliance", "plugin": "gcp", "service": "GCP/Kubernetes"}, "title": "GKE clusters should have database encryption enabled", "run_status": 8, "run_error": "relation \"gcp_kubernetes_cluster\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.kubernetes_cluster_http_load_balancing_enabled", "description": "This control ensures that GKE clusters HTTP load balancing is enabled. This control is non-complaint if HTTP load balancing is disabled.", "severity": "", "tags": {"category": "Compliance", "plugin": "gcp", "service": "GCP/Kubernetes"}, "title": "GKE clusters HTTP load balancing should be enabled", "run_status": 8, "run_error": "relation \"gcp_kubernetes_cluster\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.kubernetes_cluster_incoming_traffic_open_to_all", "description": "This control ensures that GKE clusters do not allow incoming traffic from all sources across the internet.", "severity": "", "tags": {"category": "Compliance", "plugin": "gcp", "service": "GCP/Kubernetes"}, "title": "GKE clusters should not allow incoming traffic from all sources across the internet", "run_status": 8, "run_error": "relation \"gcp_compute_firewall\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.kubernetes_cluster_intra_node_visibility_enabled", "description": "This control ensures that GKE clusters intra node visibility is enabled. This control is non-complaint if intra node visibility is disabled.", "severity": "", "tags": {"category": "Compliance", "plugin": "gcp", "service": "GCP/Kubernetes"}, "title": "GKE clusters intra node visibility should be enabled", "run_status": 8, "run_error": "relation \"gcp_kubernetes_cluster\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.kubernetes_cluster_kubernetes_alpha_enabled", "description": "This control ensures that GKE clusters kubernetes alpha is enabled.", "severity": "", "tags": {"category": "Compliance", "plugin": "gcp", "service": "GCP/Kubernetes"}, "title": "GKE clusters kubernetes alpha should be enabled", "run_status": 8, "run_error": "relation \"gcp_kubernetes_cluster\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.kubernetes_cluster_logging_enabled", "description": "This control ensures that GKE clusters logging is enabled.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Kubernetes"}, "title": "GKE clusters logging should be enabled", "run_status": 8, "run_error": "relation \"gcp_kubernetes_cluster\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.kubernetes_cluster_monitoring_enabled", "description": "This control ensures that GKE clusters monitoring is enabled.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/Kubernetes"}, "title": "GKE clusters monitoring should be enabled", "run_status": 8, "run_error": "relation \"gcp_kubernetes_cluster\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.kubernetes_cluster_network_policy_enabled", "description": "This control ensures that GKE clusters network policy is enabled.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Kubernetes"}, "title": "GKE clusters network policy should be enabled", "run_status": 8, "run_error": "relation \"gcp_kubernetes_cluster\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.kubernetes_cluster_no_default_network", "description": "This control ensures that GKE clusters does not use default network.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/Kubernetes"}, "title": "GKE clusters should not use default network", "run_status": 8, "run_error": "relation \"gcp_kubernetes_cluster\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.kubernetes_cluster_node_no_default_service_account", "description": "This control ensures that GKE clusters nodes does not uses default service account. It is recommended to create and use a least privileged service account to run your GKE cluster instead of using the default service account.", "severity": "", "tags": {"category": "Compliance", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Kubernetes"}, "title": "GKE clusters nodes should not use default service account", "run_status": 8, "run_error": "relation \"gcp_kubernetes_cluster\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.kubernetes_cluster_private_nodes_configured", "description": "This control ensures that GKE clusters private nodes are configured.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/Kubernetes"}, "title": "GKE clusters private nodes should be configured", "run_status": 8, "run_error": "relation \"gcp_kubernetes_cluster\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.kubernetes_cluster_release_channel_configured", "description": "This control ensures that GKE clusters uses release channel for version management.", "severity": "", "tags": {"category": "Compliance", "plugin": "gcp", "service": "GCP/Kubernetes"}, "title": "GKE clusters release channel should be configured", "run_status": 8, "run_error": "relation \"gcp_kubernetes_cluster\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.kubernetes_cluster_shielded_instance_integrity_monitoring_enabled", "description": "This control ensures that GKE clusters shielded nodes integrity monitoring is enabled.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/Kubernetes"}, "title": "GKE clusters shielded nodes integrity monitoring should be enabled", "run_status": 8, "run_error": "relation \"gcp_kubernetes_cluster\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.kubernetes_cluster_shielded_node_secure_boot_enabled", "description": "This control ensures that GKE clusters shielded node secure boot is enabled. This control is non-complaint if ishielded node secure boot is disabled.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/Kubernetes"}, "title": "GKE clusters shielded node secure boot should be enabled", "run_status": 8, "run_error": "relation \"gcp_kubernetes_cluster\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.kubernetes_cluster_shielded_nodes_enabled", "description": "This control ensures that GKE clusters have shielded nodes enabled.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/Kubernetes"}, "title": "GKE clusters should have shielded nodes enabled", "run_status": 8, "run_error": "relation \"gcp_kubernetes_cluster\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.kubernetes_cluster_subnetwork_private_ip_google_access_enabled", "description": "This control ensures that GKE clusters subnetworks have private google access enabled.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Kubernetes"}, "title": "Ensure Private Google Access is enabled for all subnetworks in kubernetes cluster", "run_status": 8, "run_error": "relation \"gcp_kubernetes_cluster\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.kubernetes_cluster_with_less_than_three_node_auto_upgrade_enabled", "description": "This control ensures that clusters with less than three nodes should have auto upgrade enabled.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/Kubernetes"}, "title": "GKE clusters with less than three nodes should have auto upgrade enabled", "run_status": 8, "run_error": "relation \"gcp_kubernetes_cluster\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.kubernetes_cluster_with_resource_labels", "description": "This control ensures that GKE clusters have resource labels.", "severity": "", "tags": {"category": "Compliance", "plugin": "gcp", "service": "GCP/Kubernetes"}, "title": "GKE clusters should have resource labels", "run_status": 8, "run_error": "relation \"gcp_kubernetes_cluster\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.kubernetes_cluster_zone_redundant", "description": "This control ensures that GKE clusters is located in at least three zones.", "severity": "", "tags": {"category": "Compliance", "plugin": "gcp", "service": "GCP/Kubernetes"}, "title": "GKE clusters release should be zone redundant", "run_status": 8, "run_error": "relation \"gcp_kubernetes_cluster\" does not exist (SQLSTATE 42P01)"}]}, {"group_id": "gcp_compliance.benchmark.all_controls_logging", "title": "Logging", "description": "This section contains recommendations for configuring Logging resources.", "tags": {"category": "Compliance", "plugin": "gcp", "service": "GCP/Logging", "type": "Benchmark"}, "summary": {"status": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 11}}, "groups": [], "controls": [{"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.logging_bucket_retention_policy_enabled", "description": "Enabling retention policies on log buckets will protect logs stored in cloud storage buckets from being overwritten or accidentally deleted. It is recommended to set up retention policies and configure Bucket Lock on all storage buckets that are used as log sinks.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/Logging"}, "title": "Ensure that retention policies on log buckets are configured using Bucket Lock", "run_status": 8, "run_error": "relation \"gcp_logging_sink\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.logging_metric_alert_audit_configuration_changes", "description": "Cloud audit logging records information includes the identity of the API caller, the time of the API call, the source IP address of the API caller, the request parameters, and the response elements returned by GCP services. Cloud audit logging provides a history of GCP API calls for an account, including API calls made via the console, SDKs, command-line tools, and other GCP services.", "severity": "", "tags": {"category": "Compliance", "hipaa": "true", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/Logging"}, "title": "Ensure that the log metric filter and alerts exist for Audit Configuration changes", "run_status": 8, "run_error": "relation \"gcp_monitoring_alert_policy\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.logging_metric_alert_custom_role_changes", "description": "It is recommended that a metric filter and alarm be established for changes to Identity and Access Management (IAM) role creation, deletion and updating activities.", "severity": "", "tags": {"category": "Compliance", "hipaa": "true", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/Logging"}, "title": "Ensure that the log metric filter and alerts exist for Custom Role changes", "run_status": 8, "run_error": "relation \"gcp_monitoring_alert_policy\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.logging_metric_alert_custom_role_changes_with_iam_admin_undelete_role", "description": "It is recommended that a metric filter and alarm be established for changes to Identity and Access Management (IAM) role creation, deletion, updating, and undeleting activities.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/Logging"}, "title": "Ensure that the log metric filter and alerts exist for Custom Role changes including undelete operations", "run_status": 8, "run_error": "relation \"gcp_monitoring_alert_policy\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.logging_metric_alert_firewall_rule_changes", "description": "It is recommended that a metric filter and alarm be established for Virtual Private Cloud (VPC) Network Firewall rule changes.", "severity": "", "tags": {"category": "Compliance", "hipaa": "true", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/Logging"}, "title": "Ensure that the log metric filter and alerts exist for VPC Network Firewall rule changes", "run_status": 8, "run_error": "relation \"gcp_monitoring_alert_policy\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.logging_metric_alert_network_changes", "description": "It is recommended that a metric filter and alarm be established for Virtual Private Cloud (VPC) network changes.", "severity": "", "tags": {"category": "Compliance", "hipaa": "true", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/Logging"}, "title": "Ensure that the log metric filter and alerts exist for VPC network changes", "run_status": 8, "run_error": "relation \"gcp_monitoring_alert_policy\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.logging_metric_alert_network_route_changes", "description": "It is recommended that a metric filter and alarm be established for Virtual Private Cloud (VPC) network route changes.", "severity": "", "tags": {"category": "Compliance", "hipaa": "true", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/Logging"}, "title": "Ensure that the log metric filter and alerts exist for VPC network route changes", "run_status": 8, "run_error": "relation \"gcp_monitoring_alert_policy\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.logging_metric_alert_project_ownership_assignment", "description": "In order to prevent unnecessary project ownership assignments to users/service-accounts and further misuses of projects and resources, all roles/Owner assignments should be monitored. Members (users/Service-Accounts) with a role assignment to primitive role roles/Owner are project owners.", "severity": "", "tags": {"category": "Compliance", "hipaa": "true", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/Logging"}, "title": "Ensure log metric filter and alerts exist for project ownership assignments/changes", "run_status": 8, "run_error": "relation \"gcp_monitoring_alert_policy\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.logging_metric_alert_sql_instance_configuration_changes", "description": "It is recommended that a metric filter and alarm be established for SQL instance configuration changes.", "severity": "", "tags": {"category": "Compliance", "hipaa": "true", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/Logging"}, "title": "Ensure that the log metric filter and alerts exist for SQL instance configuration changes", "run_status": 8, "run_error": "relation \"gcp_monitoring_alert_policy\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.logging_metric_alert_storage_iam_permission_changes", "description": "It is recommended that a metric filter and alarm be established for Cloud Storage Bucket IAM changes.", "severity": "", "tags": {"category": "Compliance", "hipaa": "true", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/Logging"}, "title": "Ensure that the log metric filter and alerts exist for Cloud Storage IAM permission changes", "run_status": 8, "run_error": "relation \"gcp_monitoring_alert_policy\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.logging_sink_configured_for_all_resource", "description": "It is recommended to create a sink that will export copies of all the log entries. This can help aggregate logs from multiple projects and export them to a Security Information and Event Management (SIEM).", "severity": "", "tags": {"category": "Compliance", "hipaa": "true", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/Logging"}, "title": "Ensure that sinks are configured for all log entries", "run_status": 8, "run_error": "relation \"gcp_logging_sink\" does not exist (SQLSTATE 42P01)"}]}, {"group_id": "gcp_compliance.benchmark.all_controls_organization", "title": "Organization", "description": "This section contains recommendations for configuring Organization resources.", "tags": {"category": "Compliance", "plugin": "gcp", "service": "GCP/Organization", "type": "Benchmark"}, "summary": {"status": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}}, "groups": [], "controls": [{"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.organization_essential_contacts_configured", "description": "It is recommended that Essential Contacts is configured to designate email addresses for Google Cloud services to notify of important technical or security information.", "severity": "", "tags": {"category": "Compliance", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/Organization", "soc_2_2017": "true"}, "title": "Ensure essential contacts is configured for Organization", "run_status": 8, "run_error": "relation \"gcp_organization\" does not exist (SQLSTATE 42P01)"}]}, {"group_id": "gcp_compliance.benchmark.all_controls_project", "title": "Project", "description": "This section contains recommendations for configuring Project resources.", "tags": {"category": "Compliance", "plugin": "gcp", "service": "GCP/Project", "type": "Benchmark"}, "summary": {"status": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 5}}, "groups": [], "controls": [{"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.project_access_approval_settings_enabled", "description": "GCP Access Approval enables you to require your organizations' explicit approval whenever Google support try to access your projects. You can then select users within your organization who can approve these requests through giving them a security role in IAM. All access requests display which Google Employee requested them in an email or Pub/Sub message that you can choose to Approve. This adds an additional control and logging of who in your organization approved/denied these requests.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/Project"}, "title": "Ensure 'Access Approval' is 'Enabled'", "run_status": 8, "run_error": "relation \"gcp_project\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.project_no_api_key", "description": "API keys are best reserved for situations where no alternative authentication methods are available. Within a project, there may be lingering, unused keys that still retain their permissions. The inherent insecurity of keys arises from their susceptibility to public exposure, either through web browsers or when residing on a device. It is advisable to prioritize the adoption of conventional authentication mechanisms over the reliance on API keys.", "severity": "", "tags": {"category": "Compliance", "plugin": "gcp", "service": "GCP/Project"}, "title": "Project should not have use api keys", "run_status": 8, "run_error": "relation \"gcp_apikeys_key\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.project_oslogin_enabled", "description": "Enabling OS login binds SSH certificates to IAM users and facilitates effective SSH certificate management.", "severity": "", "tags": {"category": "Compliance", "plugin": "gcp", "service": "GCP/Project"}, "title": "Ensure OS login is enabled at Project level", "run_status": 8, "run_error": "relation \"gcp_compute_project_metadata\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.project_service_cloudasset_api_enabled", "description": "GCP Cloud Asset Inventory is services that provides a historical view of GCP resources and IAM policies through a time-series database. The information recorded includes metadata on Google Cloud resources, metadata on policies set on Google Cloud projects or resources, and runtime information gathered within a Google Cloud resource.", "severity": "", "tags": {"category": "Compliance", "hipaa": "true", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/Project", "soc_2_2017": "true"}, "title": "Ensure Cloud Asset Inventory is Enabled", "run_status": 8, "run_error": "relation \"gcp_project_service\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.project_service_container_scanning_api_enabled", "description": "Container Vulnerability Scanning in Google Cloud Platform (GCP) refers to a security service that automatically performs vulnerability detection on container images stored in Container Registry and Artifact Registry. This service is designed to identify known security vulnerabilities in your container images.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/Project"}, "title": "Ensure container vulnerability scanning is enabled", "run_status": 8, "run_error": "relation \"gcp_project_service\" does not exist (SQLSTATE 42P01)"}]}, {"group_id": "gcp_compliance.benchmark.all_controls_resourcemanager", "title": "Resource Manager", "description": "This section contains recommendations for configuring Resource Manager resources.", "tags": {"category": "Compliance", "plugin": "gcp", "service": "GCP/ResourceManager", "type": "Benchmark"}, "summary": {"status": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}}, "groups": [], "controls": [{"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.audit_logging_configured_for_all_service", "description": "It is recommended that Cloud Audit Logging is configured to track all admin activities and read, write access to user data.", "severity": "", "tags": {"category": "Compliance", "hipaa": "true", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "nist_csf_v2": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/ResourceManager", "soc_2_2017": "true"}, "title": "Ensure that Cloud Audit Logging is configured properly across all services and all users from a project", "run_status": 8, "run_error": "relation \"gcp_audit_policy\" does not exist (SQLSTATE 42P01)"}]}, {"group_id": "gcp_compliance.benchmark.all_controls_sql", "title": "SQL", "description": "This section contains recommendations for configuring SQL resources.", "tags": {"category": "Compliance", "plugin": "gcp", "service": "GCP/SQL", "type": "Benchmark"}, "summary": {"status": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 34}}, "groups": [], "controls": [{"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.prevent_public_ip_cloudsql", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "hipaa": "true", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "plugin": "gcp", "service": "GCP/SQL", "severity": "high"}, "title": "Prevent a public IP from being assigned to a Cloud SQL instance", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.require_ssl_sql", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "nist_csf_v2": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/SQL", "severity": "high"}, "title": "Check if Cloud SQL instances have SSL turned on", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.sql_instance_automated_backups_enabled", "description": "It is recommended to have all SQL database instances set to enable automated backups.", "severity": "", "tags": {"category": "Compliance", "hipaa": "true", "nist_csf_v10": "true", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/SQL"}, "title": "Ensure that Cloud SQL database instances are configured with automated backups", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.sql_instance_mysql_binary_log_enabled", "description": "This controls ensures that MySql instance have binary log enabled.", "severity": "", "tags": {"category": "Compliance", "plugin": "gcp", "service": "GCP/SQL"}, "title": "MySql Instances should have binary log enabled", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.sql_instance_mysql_local_infile_database_flag_off", "description": "It is recommended to set the local_infile database flag for a Cloud SQL MySQL instance to off.", "severity": "", "tags": {"category": "Compliance", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "plugin": "gcp", "service": "GCP/SQL"}, "title": "Ensure that the 'local_infile' database flag for a Cloud SQL Mysql instance is set to 'off'", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.sql_instance_mysql_skip_show_database_flag_on", "description": "It is recommended to set skip_show_database database flag for Cloud SQL Mysql instance to on.", "severity": "", "tags": {"category": "Compliance", "hipaa": "true", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "plugin": "gcp", "service": "GCP/SQL", "soc_2_2017": "true"}, "title": "Ensure 'skip_show_database' database flag for Cloud SQL Mysql instance is set to 'on'", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.sql_instance_not_publicly_accessible", "description": "Instance addresses can be public IP or private IP. Public IP means that the instance is accessible through the public internet. In contrast, instances using only private IP are not accessible through the public internet, but are accessible through a Virtual Private Cloud (VPC).", "severity": "", "tags": {"category": "Compliance", "nist_csf_v10": "true", "nist_csf_v2": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/SQL", "soc_2_2017": "true"}, "title": "Ensure Instance IP assignment is set to private", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.sql_instance_postgresql_cloudsql_pgaudit_database_flag_enabled", "description": "Ensure cloudsql.enable_pgaudit database flag for Cloud SQL PostgreSQL instance is set to on to allow for centralized logging.", "severity": "", "tags": {"category": "Compliance", "hipaa": "true", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/SQL"}, "title": "Ensure that 'cloudsql.enable_pgaudit' Database Flag for each Cloud Sql Postgresql Instance is Set to 'on' For Centralized Logging", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.sql_instance_postgresql_log_checkpoints_database_flag_on", "description": "Ensure that the log_checkpoints database flag for the Cloud SQL PostgreSQL instance is set to on.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/SQL"}, "title": "Ensure that the 'log_checkpoints' database flag for Cloud SQL PostgreSQL instance is set to 'on'", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.sql_instance_postgresql_log_connections_database_flag_on", "description": "Enabling the log_connections setting causes each attempted connection to the server to be logged, along with successful completion of client authentication. This parameter cannot be changed after the session starts.", "severity": "", "tags": {"category": "Compliance", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/SQL", "soc_2_2017": "true"}, "title": "Ensure that the 'log_connections' database flag for Cloud SQL PostgreSQL instance is set to 'on'", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.sql_instance_postgresql_log_disconnections_database_flag_on", "description": "Enabling the log_disconnections setting logs the end of each session, including the session duration.", "severity": "", "tags": {"category": "Compliance", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "plugin": "gcp", "service": "GCP/SQL", "soc_2_2017": "true"}, "title": "Ensure that the 'log_disconnections' database flag for Cloud SQL PostgreSQL instance is set to 'on'", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.sql_instance_postgresql_log_duration_database_flag_on", "description": "Enabling the log_duration setting causes the duration of each completed statement to be logged. This does not logs the text of the query and thus behaves different from the log_min_duration_statement flag. This parameter cannot be changed after session start.", "severity": "", "tags": {"category": "Compliance", "plugin": "gcp", "service": "GCP/SQL"}, "title": "Ensure 'log_duration' database flag for Cloud SQL PostgreSQL instance is set to 'on'", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.sql_instance_postgresql_log_error_verbosity_database_flag_default_or_stricter", "description": "The log_error_verbosity flag controls the verbosity/details of messages logged. Valid values are: 'TERSE', 'DEFAULT', and 'VERBOSE'.", "severity": "", "tags": {"category": "Compliance", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "plugin": "gcp", "service": "GCP/SQL", "soc_2_2017": "true"}, "title": "Ensure 'log_error_verbosity' database flag for Cloud SQL PostgreSQL instance is set to 'DEFAULT' or stricter", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.sql_instance_postgresql_log_executor_stats_database_flag_off", "description": "The PostgreSQL executor is responsible to execute the plan handed over by the PostgreSQL planner. The executor processes the plan recursively to extract the required set of rows. The log_executor_stats flag controls the inclusion of PostgreSQL executor performance statistics in the PostgreSQL logs for each query.", "severity": "", "tags": {"category": "Compliance", "plugin": "gcp", "service": "GCP/SQL"}, "title": "Ensure 'log_executor_stats' database flag for Cloud SQL PostgreSQL instance is set to 'off'", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.sql_instance_postgresql_log_hostname_database_flag_configured", "description": "PostgreSQL logs only the IP address of the connecting hosts. The log_hostname flag controls the logging of hostnames in addition to the IP addresses logged. The performance hit is dependent on the configuration of the environment and the host name resolution setup. This parameter can only be set in the postgresql.conf file or on the server command line.", "severity": "", "tags": {"category": "Compliance", "plugin": "gcp", "service": "GCP/SQL"}, "title": "Ensure 'log_hostname' database flag for Cloud SQL PostgreSQL instance is set appropriately", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.sql_instance_postgresql_log_lock_waits_database_flag_on", "description": "Enabling the log_lock_waits flag for a PostgreSQL instance creates a log for any session waits that take longer than the allotted deadlock_timeout time to acquire a lock.", "severity": "", "tags": {"category": "Compliance", "plugin": "gcp", "service": "GCP/SQL"}, "title": "Ensure that the 'log_lock_waits' database flag for Cloud SQL PostgreSQL instance is set to 'on'", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.sql_instance_postgresql_log_min_duration_statement_database_flag_disabled", "description": "The log_min_duration_statement flag defines the minimum amount of execution time of a statement in milliseconds where the total duration of the statement is logged. Ensure that log_min_duration_statement is disabled, i.e., a value of -1 is set.", "severity": "", "tags": {"category": "Compliance", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "plugin": "gcp", "service": "GCP/SQL", "soc_2_2017": "true"}, "title": "Ensure that the 'log_min_duration_statement' database flag for Cloud SQL PostgreSQL instance is set to '-1' (disabled)", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.sql_instance_postgresql_log_min_error_statement_database_flag_configured", "description": "The log_min_error_statement flag defines the minimum message severity level that are considered as an error statement. Messages for error statements are logged with the SQL statement. Valid values include DEBUG5, DEBUG4, DEBUG3, DEBUG2, DEBUG1, INFO, NOTICE, WARNING, ERROR, LOG, FATAL, and PANIC. Each severity level includes the subsequent levels mentioned above. Ensure a value of ERROR or stricter is set.", "severity": "", "tags": {"category": "Compliance", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "plugin": "gcp", "service": "GCP/SQL", "soc_2_2017": "true"}, "title": "Ensure 'log_min_error_statement' database flag for Cloud SQL PostgreSQL instance is set to 'Error' or stricter", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.sql_instance_postgresql_log_min_messages_database_flag_error", "description": "The log_min_messages flag defines the minimum message severity level that is considered as an error statement. Messages for error statements are logged with the SQL statement. Valid values include DEBUG5, DEBUG4, DEBUG3, DEBUG2, DEBUG1, INFO, NOTICE, WARNING, ERROR, LOG, FATAL, and PANIC. Each severity level includes the subsequent levels mentioned above.", "severity": "", "tags": {"category": "Compliance", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "plugin": "gcp", "service": "GCP/SQL", "soc_2_2017": "true"}, "title": "Ensure that the 'Log_min_messages' Flag for a Cloud SQL PostgreSQL Instance is set at minimum to 'Warning'", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.sql_instance_postgresql_log_parser_stats_database_flag_off", "description": "The PostgreSQL planner/optimizer is responsible to parse and verify the syntax of each query received by the server. If the syntax is correct a parse tree is built up else an error is generated. The log_parser_stats flag controls the inclusion of parser performance statistics in the PostgreSQL logs for each query.", "severity": "", "tags": {"category": "Compliance", "plugin": "gcp", "service": "GCP/SQL"}, "title": "Ensure 'log_parser_stats' database flag for Cloud SQL PostgreSQL instance is set to 'off'", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.sql_instance_postgresql_log_planner_stats_database_flag_off", "description": "The same SQL query can be executed in multiple ways and still produce different results. The PostgreSQL planner/optimizer is responsible to create an optimal execution plan for each query. The log_planner_stats flag controls the inclusion of PostgreSQL planner performance statistics in the PostgreSQL logs for each query.", "severity": "", "tags": {"category": "Compliance", "plugin": "gcp", "service": "GCP/SQL"}, "title": "Ensure 'log_planner_stats' database flag for Cloud SQL PostgreSQL instance is set to 'off'", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.sql_instance_postgresql_log_statement_database_flag_ddl", "description": "The value of log_statement flag determined the SQL statements that are logged. Valid values are: 'none', 'ddl', 'mod', and 'all'.", "severity": "", "tags": {"category": "Compliance", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "plugin": "gcp", "service": "GCP/SQL", "soc_2_2017": "true"}, "title": "Ensure 'log_statement' database flag for Cloud SQL PostgreSQL instance is set appropriately", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.sql_instance_postgresql_log_statement_stats_database_flag_off", "description": "The log_statement_stats flag controls the inclusion of end-to-end performance statistics of a SQL query in the PostgreSQL logs for each query. This cannot be enabled with other module statistics (log_parser_stats, log_planner_stats, log_executor_stats).", "severity": "", "tags": {"category": "Compliance", "plugin": "gcp", "service": "GCP/SQL"}, "title": "Ensure 'log_statement_stats' database flag for Cloud SQL PostgreSQL instance is set to 'off'", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.sql_instance_postgresql_log_temp_files_database_flag_0", "description": "PostgreSQL can create a temporary file for actions such as sorting, hashing and temporary query results when these operations exceed work_mem. The log_temp_files flag controls logging names and the file size when it is deleted. Configuring log_temp_files to 0 causes all temporary file information to be logged, while positive values log only files whose size is greater than or equal to the specified number of kilobytes. A value of -1 disables temporary file information logging.", "severity": "", "tags": {"category": "Compliance", "plugin": "gcp", "service": "GCP/SQL"}, "title": "Ensure that the 'log_temp_files' database flag for Cloud SQL PostgreSQL instance is set to '0'", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.sql_instance_sql_3625_trace_database_flag_off", "description": "It is recommended to set 3625 (trace flag) database flag for Cloud SQL SQL Server instance to off.", "severity": "", "tags": {"category": "Compliance", "plugin": "gcp", "service": "GCP/SQL", "soc_2_2017": "true"}, "title": "Ensure '3625 (trace flag)' database flag for Cloud SQL SQL Server instance is set to 'off'", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.sql_instance_sql_3625_trace_database_flag_on", "description": "It is recommended to set 3625 (trace flag) database flag for Cloud SQL SQL Server instance to off.", "severity": "", "tags": {"category": "Compliance", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "plugin": "gcp", "service": "GCP/SQL"}, "title": "Ensure '3625 (trace flag)' database flag for Cloud SQL SQL Server instance is set to 'off'", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.sql_instance_sql_contained_database_authentication_database_flag_off", "description": "It is recommended to set contained database authentication database flag for Cloud SQL on the SQL Server instance is set to off.", "severity": "", "tags": {"category": "Compliance", "hipaa": "true", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/SQL", "soc_2_2017": "true"}, "title": "Ensure that the 'contained database authentication' database flag for Cloud SQL on the SQL Server instance is set to 'off'", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.sql_instance_sql_cross_db_ownership_chaining_database_flag_off", "description": "It is recommended to set cross db ownership chaining database flag for Cloud SQL SQL Server instance to off.", "severity": "", "tags": {"category": "Compliance", "hipaa": "true", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "plugin": "gcp", "service": "GCP/SQL", "soc_2_2017": "true"}, "title": "Ensure that the 'cross db ownership chaining' database flag for Cloud SQL SQL Server instance is set to 'off'", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.sql_instance_sql_external_scripts_enabled_database_flag_off", "description": "It is recommended to set external scripts enabled database flag for Cloud SQL SQL Server instance to off.", "severity": "", "tags": {"category": "Compliance", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "plugin": "gcp", "service": "GCP/SQL", "soc_2_2017": "true"}, "title": "Ensure 'external scripts enabled' database flag for Cloud SQL SQL Server instance is set to 'off'", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.sql_instance_sql_remote_access_database_flag_off", "description": "It is recommended to set remote access database flag for Cloud SQL SQL Server instance to off.", "severity": "", "tags": {"category": "Compliance", "nist_800_53_rev_5": "true", "plugin": "gcp", "service": "GCP/SQL", "soc_2_2017": "true"}, "title": "Ensure 'remote access' database flag for Cloud SQL SQL Server instance is set to 'off'", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.sql_instance_sql_user_connections_database_flag_configured", "description": "It is recommended to set remote access database flag for Cloud SQL SQL Server instance to off.", "severity": "", "tags": {"category": "Compliance", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "plugin": "gcp", "service": "GCP/SQL", "soc_2_2017": "true"}, "title": "Ensure 'remote access' database flag for Cloud SQL SQL Server instance is set to 'off'", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.sql_instance_sql_user_options_database_flag_not_configured", "description": "It is recommended that, user options database flag for Cloud SQL SQL Server instance should not be configured.", "severity": "", "tags": {"category": "Compliance", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "plugin": "gcp", "service": "GCP/SQL", "soc_2_2017": "true"}, "title": "Ensure 'user options' database flag for Cloud SQL SQL Server instance is not configured", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.sql_instance_with_labels", "description": "It is recommended that SQL Instance is configured with proper labels.", "severity": "", "tags": {"category": "Compliance", "plugin": "gcp", "service": "GCP/SQL"}, "title": "SQL Instances should have labels configured", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.sql_world_readable", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "forseti_security_v226": "true", "nist_800_53_rev_5": "true", "plugin": "gcp", "service": "GCP/SQL", "severity": "high", "soc_2_2017": "true"}, "title": "Check if Cloud SQL instances are world readable", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}]}, {"group_id": "gcp_compliance.benchmark.all_controls_storage", "title": "Storage", "description": "This section contains recommendations for configuring Storage resources.", "tags": {"category": "Compliance", "plugin": "gcp", "service": "GCP/Storage", "type": "Benchmark"}, "summary": {"status": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 7}}, "groups": [], "controls": [{"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.require_bucket_policy_only", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "hipaa": "true", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/Storage", "severity": "high", "soc_2_2017": "true"}, "title": "Check if Cloud Storage buckets have Bucket Only Policy turned on", "run_status": 8, "run_error": "relation \"gcp_storage_bucket\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.storage_bucket_log_not_publicly_accessible", "description": "It is recommended that IAM policy on Cloud Storage bucket used for exporting logs does not allows anonymous or public access.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Storage"}, "title": "Ensure that Cloud Storage bucket used for exporting logs is not anonymously or publicly accessible", "run_status": 8, "run_error": "relation \"gcp_logging_sink\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.storage_bucket_log_object_versioning_enabled", "description": "It is recommended that logging Cloud Storage buckets should have object versioning enabled.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Storage"}, "title": "Ensure that Cloud Storage buckets used for exporting logs have object versioning enabled", "run_status": 8, "run_error": "relation \"gcp_logging_sink\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.storage_bucket_log_retention_policy_enabled", "description": "It is recommended that Cloud Storage buckets used for exporting logs have retention policy enabled.", "severity": "", "tags": {"category": "Compliance", "nist_csf_v2": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Storage"}, "title": "Ensure that Cloud Storage buckets used for exporting logs have retention policy enabled", "run_status": 8, "run_error": "relation \"gcp_logging_sink\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.storage_bucket_log_retention_policy_lock_enabled", "description": "It is recommended that Cloud Storage buckets used for exporting logs are using bucket lock.", "severity": "", "tags": {"category": "Compliance", "hipaa": "true", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "nist_csf_v2": "true", "plugin": "gcp", "service": "GCP/Storage", "soc_2_2017": "true"}, "title": "Ensure that Cloud Storage buckets used for exporting logs are configured using bucket lock", "run_status": 8, "run_error": "relation \"gcp_logging_sink\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.storage_bucket_not_publicly_accessible", "description": "It is recommended that IAM policy on Cloud Storage bucket does not allows anonymous or public access.", "severity": "", "tags": {"category": "Compliance", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "nist_csf_v2": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Storage", "soc_2_2017": "true"}, "title": "Ensure that Cloud Storage bucket is not anonymously or publicly accessible", "run_status": 8, "run_error": "relation \"gcp_storage_bucket\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.storage_bucket_uniform_access_enabled", "description": "It is recommended that uniform bucket-level access is enabled on Cloud Storage buckets.", "severity": "", "tags": {"category": "Compliance", "plugin": "gcp", "service": "GCP/Storage"}, "title": "Ensure that Cloud Storage buckets have uniform bucket-level access enabled", "run_status": 8, "run_error": "relation \"gcp_storage_bucket\" does not exist (SQLSTATE 42P01)"}]}], "controls": null}], "controls": null}