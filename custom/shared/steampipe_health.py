"""
Steampipe Health Check and Recovery Module
Ensures Steampipe service is running before benchmark execution
"""

import subprocess
import time
import logging
import os
import threading
import re
import tempfile
import shutil

logger = logging.getLogger(__name__)

# Global lock to prevent concurrent service operations
_service_lock = threading.Lock()
_service_initialized = False

def kill_steampipe_processes():
    """Kill all Steampipe processes more aggressively"""
    try:
        # First try to stop the service normally
        try:
            subprocess.run(
                ["steampipe", "service", "stop", "--force"],
                capture_output=True,
                text=True,
                timeout=15,
                check=False
            )
            time.sleep(1)
        except:
            pass
        
        # Get PIDs of steampipe processes
        try:
            ps_result = subprocess.run(
                ["ps", "aux"],
                capture_output=True,
                text=True,
                check=False
            )
            
            for line in ps_result.stdout.splitlines():
                if "steampipe" in line and "ps aux" not in line:
                    parts = line.split()
                    if len(parts) > 1:
                        pid = parts[1]
                        try:
                            subprocess.run(["kill", "-9", pid], check=False)
                            logger.info(f"Killed Steampipe process with PID: {pid}")
                        except:
                            pass
        except:
            pass
        
        # Also try pkill as backup
        subprocess.run(["pkill", "-9", "-f", "steampipe"], check=False, capture_output=True)
        subprocess.run(["pkill", "-9", "-f", "postgres.*steampipe"], check=False, capture_output=True)
        
        time.sleep(2)
        logger.info("Completed Steampipe process cleanup")
    except Exception as e:
        logger.error(f"Error killing Steampipe processes: {e}")

def check_steampipe_health():
    """Check if Steampipe service is healthy"""
    try:
        # Try a simple query with reasonable timeout for Cloud Run
        result = subprocess.run(
            ["steampipe", "query", "select 1 as test"],
            capture_output=True,
            text=True,
            timeout=30
        )
        return result.returncode == 0
    except subprocess.TimeoutExpired:
        logger.error("Steampipe health check timed out")
        return False
    except Exception as e:
        logger.error(f"Steampipe health check failed: {e}")
        return False

def check_and_install_gcp_plugin():
    """Check if GCP plugin is installed and install if needed"""
    try:
        # Debug environment
        logger.info(f"Checking GCP plugin - HOME: {os.environ.get('HOME')}")
        logger.info(f"Current working directory: {os.getcwd()}")
        logger.info(f"User ID: {os.getuid()}")
        
        # Check current plugins
        logger.info("Checking installed Steampipe plugins...")
        
        # First, try a simple steampipe command to see if it works
        try:
            version_result = subprocess.run(
                ["steampipe", "--version"],
                capture_output=True,
                text=True,
                timeout=5
            )
            logger.info(f"Steampipe version check: {version_result.returncode}")
            if version_result.stdout:
                logger.info(f"Steampipe version: {version_result.stdout.strip()}")
        except Exception as e:
            logger.error(f"Steampipe version check failed: {e}")
            return False
        
        list_result = subprocess.run(
            ["steampipe", "plugin", "list"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if list_result.returncode == 0:
            logger.info(f"Installed plugins:\n{list_result.stdout}")
            
            # Check if GCP plugin is in the list
            if "gcp" not in list_result.stdout:
                logger.warning("GCP plugin not found in installed plugins")
                
                # Check if custom plugin exists
                custom_plugin_path = "/home/<USER>/gcp-steampipe-plugin/steampipe-plugin-gcp"
                if os.path.exists(custom_plugin_path):
                    logger.info(f"Found custom GCP plugin at {custom_plugin_path}")
                    
                    # Install the custom plugin
                    install_result = subprocess.run(
                        ["steampipe", "plugin", "install", custom_plugin_path],
                        capture_output=True,
                        text=True,
                        timeout=30
                    )
                    
                    if install_result.returncode == 0:
                        logger.info("Custom GCP plugin installed successfully")
                        return True
                    else:
                        logger.error(f"Failed to install custom GCP plugin: {install_result.stderr}")
                        
                        # Try installing from registry as fallback
                        logger.info("Attempting to install GCP plugin from registry...")
                        registry_result = subprocess.run(
                            ["steampipe", "plugin", "install", "gcp"],
                            capture_output=True,
                            text=True,
                            timeout=60
                        )
                        
                        if registry_result.returncode == 0:
                            logger.info("GCP plugin installed from registry")
                            return True
                        else:
                            logger.error(f"Failed to install GCP plugin from registry: {registry_result.stderr}")
                            return False
                else:
                    logger.warning("Custom GCP plugin not found, installing from registry...")
                    # Install from registry
                    install_result = subprocess.run(
                        ["steampipe", "plugin", "install", "gcp"],
                        capture_output=True,
                        text=True,
                        timeout=60
                    )
                    
                    if install_result.returncode == 0:
                        logger.info("GCP plugin installed from registry")
                        return True
                    else:
                        logger.error(f"Failed to install GCP plugin: {install_result.stderr}")
                        return False
            else:
                logger.info("GCP plugin is already installed")
                return True
        else:
            logger.error(f"Failed to list plugins: {list_result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"Error checking/installing GCP plugin: {e}")
        return False

def clean_steampipe_state():
    """Clean up Steampipe state files"""
    try:
        # Remove various state files that might cause issues
        state_dirs = [
            os.path.expanduser("~/.steampipe/internal"),
            os.path.expanduser("~/.steampipe/db/14.2.0/data"),
            os.path.expanduser("~/.steampipe/db/14.2.0/postgres.pid"),
            "/tmp/.s.PGSQL.9193",
            "/tmp/.s.PGSQL.9193.lock"
        ]
        
        for path in state_dirs:
            if os.path.exists(path):
                if os.path.isfile(path):
                    try:
                        os.remove(path)
                        logger.info(f"Removed state file: {path}")
                    except:
                        pass
                else:
                    try:
                        subprocess.run(["rm", "-rf", path], check=False)
                        logger.info(f"Cleaned state directory: {path}")
                    except:
                        pass
                        
        # Also clean any lock files
        subprocess.run(["find", "/tmp", "-name", "*.steampipe.lock", "-delete"], check=False, capture_output=True)
        
    except Exception as e:
        logger.error(f"Error cleaning Steampipe state: {e}")

def start_steampipe_service():
    """Start Steampipe service with proper cleanup"""
    try:
        # First, ensure no existing processes
        kill_steampipe_processes()
        
        # Clean state files
        clean_steampipe_state()
        
        # Wait a bit after cleanup
        time.sleep(3)
        
        # Check and install GCP plugin if needed
        check_and_install_gcp_plugin()
        
        # Start service with explicit database settings
        env = os.environ.copy()
        env['STEAMPIPE_DATABASE_START_TIMEOUT'] = '60'
        env['STEAMPIPE_LOG_LEVEL'] = 'info'
        
        # Try to start the service - first try with alternative port if stuck
        logger.info("Starting Steampipe service...")
        
        # Check if default port is stuck
        port_check = subprocess.run(
            ["steampipe", "service", "status"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        # If we're in unknown state, try alternative port
        if "unknown state" in port_check.stderr or "unknown state" in port_check.stdout:
            logger.warning("Default port appears stuck, trying alternative port 9194...")
            result = subprocess.run(
                ["steampipe", "service", "start", "--database-listen=local", "--database-port=9194"],
                capture_output=True,
                text=True,
                timeout=60,
                env=env
            )
            
            if result.returncode == 0:
                logger.info("Started on alternative port 9194")
                # Update environment for this port
                os.environ['STEAMPIPE_DATABASE_PORT'] = '9194'
                # Wait and check health
                time.sleep(5)
                if check_steampipe_health():
                    return True
        
        # Try normal start
        result = subprocess.run(
            ["steampipe", "service", "start", "--database-listen=local", "--database-port=9193"],
            capture_output=True,
            text=True,
            timeout=60,
            env=env
        )
        
        # Check if we got the unknown state error
        if "unknown state" in result.stderr and "PID:" in result.stderr:
            # Extract PID from error message
            pid_match = re.search(r'PID:\s*(\d+)', result.stderr)
            if pid_match:
                pid = pid_match.group(1)
                logger.warning(f"Found stuck Steampipe process with PID {pid}")
                
            # Since we can't kill in Cloud Run, try working around it
            logger.warning("Unable to clean stuck process in containerized environment")
            logger.info("Attempting to use Steampipe in current state...")
            
            # Check if it's actually working despite the error
            if check_steampipe_health():
                logger.info("Steampipe appears to be working despite error message")
                return True
            
            # As last resort, return false to trigger complete reset
            return False
        
        if result.returncode != 0 and result.stderr:
            logger.error(f"Steampipe start failed: {result.stderr}")
            # Try to see if it's actually running anyway
            if check_steampipe_health():
                logger.info("Steampipe is running despite error")
                return True
            return False
        
        # Wait for service to be ready
        logger.info("Waiting for Steampipe service to be ready...")
        for i in range(15):  # Increased attempts
            time.sleep(2)
            if check_steampipe_health():
                logger.info("Steampipe service started successfully")
                return True
            logger.info(f"Health check attempt {i+1}/15...")
        
        logger.error("Steampipe service failed to become healthy")
        return False
        
    except subprocess.TimeoutExpired:
        logger.error("Steampipe start command timed out")
        return False
    except Exception as e:
        logger.error(f"Failed to start Steampipe: {e}")
        return False

def restart_steampipe_service():
    """Restart Steampipe service with proper cleanup"""
    try:
        logger.info("Restarting Steampipe service...")
        
        # Stop service forcefully
        try:
            subprocess.run(
                ["steampipe", "service", "stop", "--force"],
                capture_output=True,
                text=True,
                timeout=10,
                check=False
            )
        except subprocess.TimeoutExpired:
            logger.warning("Stop command timed out, proceeding with cleanup")
        
        # Ensure processes are killed
        kill_steampipe_processes()
        
        # Start service
        return start_steampipe_service()
        
    except Exception as e:
        logger.error(f"Failed to restart Steampipe: {e}")
        return False

def ensure_steampipe_running():
    """Ensure Steampipe is running, restart if needed - Cloud Run optimized"""
    global _service_initialized
    
    with _service_lock:
        # If service was already initialized successfully, just check health
        if _service_initialized and check_steampipe_health():
            logger.info("Steampipe service is healthy")
            return
        
        # First, try a quick health check - if it works, we're good to go
        if check_steampipe_health():
            logger.info("Steampipe is working despite status concerns")
            _service_initialized = True
            return
        
        # Try to check service status but be more forgiving
        try:
            health_result = subprocess.run(
                ["steampipe", "service", "status"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            # If we get unknown state but health check might work, try working with it
            if "unknown state" in health_result.stdout or "unknown state" in health_result.stderr:
                logger.warning("Steampipe is in unknown state, but attempting to work with it...")
                # Give it a moment and try health check again
                time.sleep(2)
                if check_steampipe_health():
                    logger.info("Successfully working with steampipe despite unknown state")
                    _service_initialized = True
                    return
                
                # Only restart if health check fails
                logger.warning("Health check failed, attempting restart...")
                if restart_steampipe_service():
                    _service_initialized = True
                    return
        except:
            # If status check fails, just continue with normal startup
            logger.info("Status check failed, attempting normal startup...")
        
        # Service needs to be started or restarted
        logger.info("Initializing Steampipe service...")
        
        # Try to start/restart service with fewer attempts to avoid long delays
        if start_steampipe_service():
            _service_initialized = True
            logger.info("Steampipe service is healthy")
            return
        
        # One retry with a brief wait
        logger.warning("First start attempt failed, retrying once...")
        time.sleep(3)
        if start_steampipe_service():
            _service_initialized = True
            logger.info("Steampipe service started on retry")
            return
        
        # Only do complete reset as absolute last resort
        logger.warning("Normal start failed, attempting complete reset as last resort...")
        if reset_steampipe_completely():
            if start_steampipe_service():
                _service_initialized = True
                logger.info("Steampipe service started after complete reset")
                return
        
        raise Exception("Failed to start Steampipe service after all attempts including reset")

def reset_steampipe_completely():
    """Complete reset of Steampipe - nuclear option"""
    try:
        logger.warning("Performing complete Steampipe reset...")
        
        # Stop everything - increased timeout for Cloud Run environment
        try:
            subprocess.run(["steampipe", "service", "stop", "--force"], check=False, capture_output=True, timeout=30)
        except subprocess.TimeoutExpired:
            logger.warning("Steampipe stop command timed out after 30 seconds, continuing with process cleanup")
        
        # Kill all processes
        subprocess.run(["pkill", "-9", "-f", "steampipe"], check=False, capture_output=True)
        subprocess.run(["pkill", "-9", "-f", "postgres"], check=False, capture_output=True)
        
        # Remove Steampipe data but preserve configuration files
        steampipe_dir = os.path.expanduser("~/.steampipe")
        if os.path.exists(steampipe_dir):
            # Backup configuration files from multiple locations
            config_dir = os.path.join(steampipe_dir, "config")
            local_config_dir = "steampipe-config"  # Local directory that might have configs
            config_files = {}

            # First try to backup from the main config directory
            if os.path.exists(config_dir):
                for filename in os.listdir(config_dir):
                    if filename.endswith('.spc'):
                        file_path = os.path.join(config_dir, filename)
                        try:
                            with open(file_path, 'r') as f:
                                config_files[filename] = f.read()
                            logger.info(f"Backed up configuration file: {filename}")
                        except Exception as e:
                            logger.warning(f"Failed to backup {filename}: {e}")

            # Also check local steampipe-config directory as fallback
            if os.path.exists(local_config_dir):
                for filename in os.listdir(local_config_dir):
                    if filename.endswith('.spc') and filename not in config_files:
                        file_path = os.path.join(local_config_dir, filename)
                        try:
                            with open(file_path, 'r') as f:
                                config_files[filename] = f.read()
                            logger.info(f"Backed up configuration file from local dir: {filename}")
                        except Exception as e:
                            logger.warning(f"Failed to backup from local dir {filename}: {e}")

            # Remove the entire directory (force removal of busy files)
            subprocess.run(["rm", "-rf", steampipe_dir], check=False)
            # Additional cleanup for stubborn files
            subprocess.run(["find", steampipe_dir, "-type", "f", "-delete"], check=False)
            subprocess.run(["find", steampipe_dir, "-type", "d", "-delete"], check=False)
            logger.info("Removed Steampipe directory")

            # Restore configuration files
            if config_files:
                os.makedirs(config_dir, exist_ok=True)
                for filename, content in config_files.items():
                    file_path = os.path.join(config_dir, filename)
                    try:
                        with open(file_path, 'w') as f:
                            f.write(content)
                        logger.info(f"Restored configuration file: {filename}")
                    except Exception as e:
                        logger.error(f"Failed to restore {filename}: {e}")
                logger.info("Restored all configuration files")
        
        # Wait before reinitializing
        time.sleep(5)
        
        # Reinitialize Steampipe
        logger.info("Reinitializing Steampipe...")
        subprocess.run(["steampipe", "plugin", "install", "steampipe"], check=False, capture_output=True, timeout=30)
        
        return True
    except Exception as e:
        logger.error(f"Failed to reset Steampipe: {e}")
        return False

def initialize_steampipe_on_startup():
    """Initialize Steampipe service on container startup"""
    try:
        logger.info("Initializing Steampipe service on startup...")
        
        # First check and install GCP plugin if needed
        if not check_and_install_gcp_plugin():
            logger.warning("GCP plugin installation failed, but continuing...")
        
        # First try normal startup
        try:
            ensure_steampipe_running()
            logger.info("Steampipe service initialized successfully")
            return
        except Exception as e:
            logger.warning(f"Normal startup failed: {e}, trying complete reset...")
            
        # If normal startup fails, try complete reset
        if reset_steampipe_completely():
            # Try to start again after reset
            ensure_steampipe_running()
            logger.info("Steampipe service initialized successfully after reset")
        else:
            raise Exception("Failed to initialize Steampipe even after reset")
            
    except Exception as e:
        logger.error(f"Failed to initialize Steampipe on startup: {e}")
        # Don't fail container startup, but log the error
        # Service will be started on first request